<?php

namespace App\Http\Controllers;

use App\Models\Feature;
use Illuminate\Http\Request;
use App\Models\HomePageSection;

class HomeController extends Controller
{
    public function index()
    {
        $sections = HomePageSection::visible()->ordered()->get();

        $sections = $sections->map(function ($section) {
            return [
                'nombre' => $section->nombre,
                'slug' => $section->slug,
                'tipo' => $section->tipo,
                'slug_categoria' => $section->slug_categoria,
                'orden' => $section->orden,
                'visible' => $section->visible,
                'limite' => $section->limite,
            ];
        });

        $features = Feature::whereHas('language', fn($q) => $q->where('code', app()->getLocale()))
            ->publicado()
            ->ordered()
            ->get();

        $testimonials = \App\Models\Testimonial::whereHas('language', fn($q) => $q->where('code', app()->getLocale()))
            ->publicado()
            ->ordered()
            ->get();

        // $featuredProduct = \App\Models\Product::where('activo', true)
        //     ->with(['languages'])
        //     ->inRandomOrder()
        //     ->first();
        $featuredProduct = '';

        return view('home', compact('sections', 'features', 'testimonials', 'featuredProduct'));
    }
}
