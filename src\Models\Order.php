<?php

namespace LBCDev\Ecommerce\Models;

use Illuminate\Database\Eloquent\Model;
use LBCDev\Ecommerce\Enums\OrderStatus;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use LBCDev\Ecommerce\Database\Factories\OrderFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    use HasFactory;

    protected $table = 'lbcdev_ecommerce_orders';

    protected $fillable = [
        'user_id',
        'order_number',
        'status',
        'subtotal',
        'tax_amount',
        'total',
        'currency',
        'billing_address_id',
        'shipping_address_id',
        'notes',
        'shipped_at',
        'delivered_at',
    ];

    protected $casts = [
        'subtotal' => 'float',
        'tax_amount' => 'float',
        'total' => 'float',
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
    ];

    /**
     * Generar número de orden único.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (!$order->order_number) {
                $order->order_number = static::generateOrderNumber();
            }
        });
    }

    /**
     * Relación con los items de la orden.
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Relación con el usuario (opcional).
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(config('auth.providers.users.model'));
    }

    /**
     * Crear orden desde carrito.
     */
    public static function createFromCart(Cart $cart, array $customerData = [], ?int $userId = null): self
    {
        if (empty($cart->getItems())) {
            throw new \InvalidArgumentException('Cannot create order from empty cart');
        }

        $order = static::create([
            'user_id' => $userId,
            'status' => OrderStatus::PENDING->value,
            'subtotal' => $cart->getTotal(),
            'tax_amount' => $cart->getTotalWithTax() - $cart->getTotal(),
            'total' => $cart->getTotalWithTax(),
            'currency' => config('ecommerce.currency', 'EUR'),
            'customer_name' => $customerData['name'] ?? null,
            'customer_email' => $customerData['email'] ?? null,
            'customer_phone' => $customerData['phone'] ?? null,
            'billing_address' => $customerData['billing_address'] ?? null,
            'shipping_address' => $customerData['shipping_address'] ?? null,
            'notes' => $customerData['notes'] ?? null,
        ]);

        // Crear items de la orden
        foreach ($cart->getItems() as $cartItem) {
            $order->items()->create([
                'sellable_type' => get_class($cartItem->getItem()),
                'sellable_id' => $cartItem->getId(),
                'name' => $cartItem->getName(),
                'quantity' => $cartItem->getQuantity(),
                'unit_price' => $cartItem->getUnitPrice(),
                'tax_rate' => $cartItem->getItem()->getSellableTaxRate(),
                'subtotal' => $cartItem->getSubtotal(),
                'tax_amount' => $cartItem->getSubtotalWithTax() - $cartItem->getSubtotal(),
                'total' => $cartItem->getSubtotalWithTax(),
            ]);
        }

        return $order;
    }

    /**
     * Cambiar estado de la orden.
     */
    public function updateStatus(string $status): void
    {
        $validStatuses = OrderStatus::getPossibleValues();

        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException("Invalid status: {$status}");
        }

        $this->update(['status' => $status]);

        // Actualizar timestamps automáticamente
        if ($status === OrderStatus::SHIPPED->value && !$this->shipped_at) {
            $this->update(['shipped_at' => now()]);
        }

        if ($status === OrderStatus::DELIVERED->value && !$this->delivered_at) {
            $this->update(['delivered_at' => now()]);
        }
    }

    /**
     * Verificar si la orden puede ser cancelada.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, [
            OrderStatus::PENDING->value,
            OrderStatus::PROCESSING->value
        ]);
    }

    /**
     * Verificar si la orden puede ser reembolsada.
     */
    public function canBeRefunded(): bool
    {
        return in_array($this->status, [
            OrderStatus::PROCESSING->value,
            OrderStatus::SHIPPED->value,
            OrderStatus::DELIVERED->value
        ]);
    }

    /**
     * Obtener estados disponibles.
     */
    public static function getAvailableStatuses(): array
    {
        return array_column(OrderStatus::cases(), 'label', 'value');
    }

    /**
     * Generar número de orden único.
     */
    protected static function generateOrderNumber(): string
    {
        $prefix = config('ecommerce.order_number_prefix', 'ORD');
        $timestamp = now()->format('Ymd');

        // Obtener el último número del día
        $lastOrder = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastOrder ? (int) substr($lastOrder->order_number, -4) + 1 : 1;

        return $prefix . '-' . $timestamp . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Scope para filtrar por estado.
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope para órdenes recientes.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    protected static function newFactory()
    {
        return OrderFactory::new();
    }
}
