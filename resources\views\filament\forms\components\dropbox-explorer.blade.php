@php
    $oauthServiceField = $getOauthServiceField();
    $oauthServiceId = null;
    
    if ($oauthServiceField) {
        $oauthServiceId = data_get($getRecord(), $oauthServiceField) ?? $this->form->getRawState()[$oauthServiceField] ?? null;
    }
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div class="space-y-4"
         x-data="{
             fieldValue: @entangle($getStatePath()).live
         }"
         x-init="
             window.addEventListener('fileSelected-{{ str_replace('.', '-', $getStatePath()) }}', (event) => {
                 fieldValue = event.detail.fileUrl;
             });
         ">
        {{-- Hidden input to store the selected file URL --}}
        <input
            type="hidden"
            {!! $applyStateBindingModifiers('wire:model') !!}="{{ $getStatePath() }}"
            x-model="fieldValue"
        />

        {{-- Current selected file display --}}
        @if($getState())
            <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 13.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-900">Selected file:</span>
                    </div>
                    <button
                        type="button"
                        wire:click="$set('{{ $getStatePath() }}', '')"
                        class="text-red-600 hover:text-red-800"
                        title="Clear selection"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="mt-2">
                    <a 
                        href="{{ $getState() }}" 
                        target="_blank" 
                        class="text-sm text-blue-600 hover:text-blue-800 hover:underline break-all"
                    >
                        {{ $getState() }}
                    </a>
                </div>
            </div>
        @endif

        {{-- Dropbox Explorer Component --}}
        @if($oauthServiceId)
            @livewire('oauth-file-explorer', [
                'oauthServiceId' => $oauthServiceId,
                'fieldName' => $getStatePath(),
                'acceptedMimeTypes' => $getAcceptedMimeTypes()
            ], key($getStatePath() . '-' . $oauthServiceId))
        @else
            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-yellow-800">
                            Dropbox service required
                        </p>
                        <p class="text-xs text-yellow-700 mt-1">
                            @if($oauthServiceField)
                                Please select a Dropbox service in the "{{ $oauthServiceField }}" field first.
                            @else
                                Please configure the OAuth service field for this Dropbox explorer.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        @endif

        {{-- Instructions --}}
        @if(!$getState() && $oauthServiceId)
            <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-blue-800">How to use:</p>
                        <ul class="text-xs text-blue-700 mt-1 space-y-1">
                            <li>• Browse folders by clicking on them</li>
                            <li>• Click on a file to select it</li>
                            <li>• Use the search bar to find specific files</li>
                            <li>• Use breadcrumbs to navigate back to parent folders</li>
                        </ul>
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-dynamic-component>
