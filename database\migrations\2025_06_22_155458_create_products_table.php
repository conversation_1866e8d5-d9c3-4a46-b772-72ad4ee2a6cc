<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::create('products', function (Blueprint $table) {
        //     $table->id();
        //     $table->string('nombre');
        //     $table->string('slug')->unique();
        //     $table->text('descripcion_corta')->nullable();
        //     $table->text('descripcion_larga')->nullable();
        //     $table->decimal('precio', 10, 2)->default(0);
        //     $table->decimal('precio_descuento', 10, 2)->default(0);
        //     $table->boolean('activo')->default(true);
        //     $table->integer('orden')->default(0);
        //     $table->timestamps();
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::dropIfExists('products');
    }
};
