<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;

class CheckoutControllerTest extends TestCase
{
    use RefreshDatabase, WithoutMiddleware;

    protected User $user;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        $this->product = Product::factory()->create([
            'nombre' => 'Test Product',
            'descripcion_corta' => 'Test description',
            'precio' => 99.99,
            'activo' => true,
            'slug' => 'test-product',
        ]);
    }

    public function test_checkout_redirects_when_cart_is_empty(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/checkout/');

        $response->assertRedirect()
            ->assertSessionHas('error', 'Tu carrito está vacío.');
    }

    public function test_checkout_summary_returns_error_for_empty_cart(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson('/checkout/summary');

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Tu carrito está vacío.',
            ]);
    }

    public function test_checkout_summary_works_with_cart_items(): void
    {
        // Add item to cart via session
        session(['cart' => [
            $this->product->id => [
                'id' => $this->product->id,
                'titulo' => $this->product->titulo,
                'precio' => $this->product->precio,
                'quantity' => 2,
                'slug' => $this->product->slug,
            ]
        ]]);

        $response = $this->actingAs($this->user)
            ->getJson('/checkout/summary');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'total' => 199.98,
                'has_changes' => false,
            ]);
    }

    public function test_checkout_process_fails_with_empty_cart(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/checkout/process', [
                'gateway' => 'stripe',
                'terms' => true,
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Tu carrito está vacío.',
            ]);
    }

    public function test_checkout_process_validates_required_fields(): void
    {
        // Add item to cart
        session(['cart' => [
            $this->product->id => [
                'id' => $this->product->id,
                'titulo' => $this->product->titulo,
                'precio' => $this->product->precio,
                'quantity' => 1,
                'slug' => $this->product->slug,
            ]
        ]]);

        $response = $this->actingAs($this->user)
            ->postJson('/checkout/process', [
                // Missing required fields
            ]);

        $response->assertStatus(422); // Validation error
    }

    public function test_success_page_requires_valid_order(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/checkout/success/999');

        $response->assertRedirect()
            ->assertSessionHas('error', 'Pedido no encontrado.');
    }

    public function test_cancel_page_loads(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/checkout/cancel');

        $response->assertStatus(200);
    }
}
