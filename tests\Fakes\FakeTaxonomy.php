<?php

namespace Tests\Fakes;

/**
 * Modelo falso que simula una taxonomía para tests.
 */
class FakeTaxonomy
{
    public $id;
    public $name;
    public $slug;
    public $type;
    public $parent_id;

    public function __construct($id = 1, $name = 'Test Taxonomy', $slug = 'test-taxonomy', $type = 'test-type', $parent_id = null)
    {
        $this->id = $id;
        $this->name = $name;
        $this->slug = $slug;
        $this->type = $type;
        $this->parent_id = $parent_id;
    }

    public function getKey()
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getType(): string
    {
        return $this->type;
    }
}
