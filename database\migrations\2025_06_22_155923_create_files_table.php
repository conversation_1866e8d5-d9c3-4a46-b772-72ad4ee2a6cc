<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            $table->string('nombre');
            $table->foreignId('oauth_service_id')->references('id')->on('oauth_services');
            $table->string('url');
            $table->json('metadata')->nullable();
            $table->foreignId('language_id')->references('id')->on('languages');
            $table->integer('orden')->default(0); // opcional, para ordenar descargas
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
