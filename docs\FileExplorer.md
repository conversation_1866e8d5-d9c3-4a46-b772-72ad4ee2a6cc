# File Explorer System

## Descripción

El sistema File Explorer es una arquitectura extensible que permite integrar múltiples servicios de almacenamiento en la nube (Google Drive, Dropbox, OneDrive, etc.) en formularios de Filament. El sistema está diseñado para ser fácilmente extensible y reutilizable.

## Arquitectura

### Componentes principales

1. **FileExplorerInterface** - Interfaz que define los métodos que deben implementar todos los exploradores
2. **AbstractFileExplorer** - Clase base abstracta que proporciona funcionalidad común
3. **FileExplorerFactory** - Factory para crear instancias de exploradores según el tipo de servicio
4. **FileExplorer (Livewire)** - Componente Livewire genérico para la interfaz de usuario
5. **FileExplorer (Filament)** - Componente de campo de formulario para Filament

### Exploradores implementados

- **GoogleDriveService** - Explorador para Google Drive (refactorizado)
- **DropboxExplorer** - Explorador para Dropbox (ejemplo de implementación)

## Uso básico

### En un recurso de Filament

```php
use App\Filament\Forms\Components\FileExplorer;

public static function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\Select::make('oauth_service_id')
                ->relationship('oauthService', 'name', 
                    fn($query) => $query->where('is_active', true)
                                       ->where('access_token', '!=', null)
                )
                ->live()
                ->required()
                ->label('Storage Service'),

            FileExplorer::make('file_url')
                ->label('File')
                ->oauthServiceField('oauth_service_id')
                ->required()
                ->helperText('Browse and select a file from your storage service'),
        ]);
}
```

### Filtros de tipos de archivo

```php
FileExplorer::make('document_url')
    ->acceptDocuments()  // Solo documentos
    ->oauthServiceField('oauth_service_id');

FileExplorer::make('video_url')
    ->acceptVideos()     // Solo videos
    ->oauthServiceField('oauth_service_id');

FileExplorer::make('image_url')
    ->acceptImages()     // Solo imágenes
    ->oauthServiceField('oauth_service_id');

// O tipos personalizados
FileExplorer::make('custom_url')
    ->acceptedMimeTypes(['application/pdf', 'text/plain'])
    ->oauthServiceField('oauth_service_id');
```

## Crear un nuevo explorador

### 1. Crear la clase del explorador

```php
<?php

namespace App\Services\FileExplorers;

class MyServiceExplorer extends AbstractFileExplorer
{
    protected $serviceClient;

    public function getServiceType(): string
    {
        return 'my_service';
    }

    protected function initializeService(): void
    {
        $this->ensureValidToken();
        
        // Inicializar cliente del servicio
        $this->serviceClient = new MyServiceClient($this->oauthService->access_token);
    }

    public function listFiles(string $folderId = 'root', int $pageSize = 50): array
    {
        $this->ensureValidToken();
        
        if (!$this->serviceClient) {
            $this->initializeService();
        }

        try {
            // Implementar lógica específica del servicio
            $files = $this->serviceClient->listFiles($folderId, $pageSize);
            
            $formattedFiles = [];
            foreach ($files as $file) {
                $formattedFiles[] = $this->formatFileData($file);
            }

            return [
                'files' => $formattedFiles,
                'nextPageToken' => $files->getNextPageToken() ?? null
            ];
        } catch (\Exception $e) {
            throw new \Exception('Error listing files: ' . $e->getMessage());
        }
    }

    public function getFile(string $fileId): array
    {
        // Implementar obtención de archivo específico
    }

    public function searchFiles(string $query, string $folderId = 'root'): array
    {
        // Implementar búsqueda de archivos
    }

    public function getBreadcrumb(string $folderId): array
    {
        // Implementar navegación breadcrumb
    }

    protected function performConnectionTest(): bool
    {
        try {
            if (!$this->serviceClient) {
                $this->initializeService();
            }

            // Probar conexión con el servicio
            $this->serviceClient->testConnection();
            return true;
        } catch (\Exception) {
            return false;
        }
    }

    protected function formatFileData($file): array
    {
        // Convertir datos del servicio al formato estándar
        return $this->getStandardFileStructure(
            $file->getId(),
            $file->getName(),
            $file->getMimeType(),
            $file->isFolder(),
            [
                'size' => $file->getSize(),
                'modifiedTime' => $file->getModifiedTime(),
                'webViewLink' => $file->getWebViewLink(),
                // ... otros campos específicos
            ]
        );
    }

    public function getSupportedMimeTypes(): array
    {
        return [
            // Lista de tipos MIME soportados por el servicio
        ];
    }
}
```

### 2. Registrar en el Factory

```php
// En FileExplorerFactory.php
return match ($oauthService->service_type) {
    'google_drive' => new GoogleDriveService($oauthService),
    'dropbox' => new DropboxExplorer($oauthService),
    'my_service' => new MyServiceExplorer($oauthService), // Agregar aquí
    // ...
};

// Actualizar también getSupportedServiceTypes()
public static function getSupportedServiceTypes(): array
{
    return [
        'google_drive',
        'dropbox',
        'my_service', // Agregar aquí
        // ...
    ];
}
```

### 3. Configurar OAuth Manager

Asegúrate de que el servicio esté configurado en el paquete `lbcdev/oauth-manager`:

```php
// En config/oauth-manager.php
'services' => [
    'my_service' => [
        'name' => 'My Service',
        'slug' => 'my-service',
        'icon' => 'heroicon-o-cloud',
        'provider' => \LBCDev\OAuthManager\Providers\MyServiceProvider::class,
        'scopes' => ['read', 'write'],
        'fields' => [
            'client_id' => env('MY_SERVICE_CLIENT_ID'),
            'client_secret' => env('MY_SERVICE_CLIENT_SECRET'),
        ]
    ],
];
```

## Métodos requeridos

### FileExplorerInterface

Todos los exploradores deben implementar estos métodos:

- `listFiles(string $folderId, int $pageSize): array`
- `getFile(string $fileId): array`
- `searchFiles(string $query, string $folderId): array`
- `getBreadcrumb(string $folderId): array`
- `testConnection(): bool`
- `getServiceType(): string`
- `getSupportedMimeTypes(): array`
- `supportsMimeType(string $mimeType): bool`

### AbstractFileExplorer

Métodos abstractos que deben implementarse:

- `initializeService(): void`
- `formatFileData($file): array`
- `performConnectionTest(): bool`

## Estructura de datos estándar

### Archivo/Carpeta

```php
[
    'id' => 'file_id',
    'name' => 'filename.ext',
    'mimeType' => 'application/pdf',
    'isFolder' => false,
    'size' => 1024,
    'sizeFormatted' => '1 KB',
    'modifiedTime' => '2023-01-01T12:00:00Z',
    'webViewLink' => 'https://...',
    'webContentLink' => 'https://...',
    'iconLink' => 'https://...',
    'parents' => ['parent_folder_id'],
    'serviceType' => 'google_drive',
    'downloadUrl' => 'https://...'
]
```

### Breadcrumb

```php
[
    ['id' => 'root', 'name' => 'Root'],
    ['id' => 'folder1', 'name' => 'Folder 1'],
    ['id' => 'folder2', 'name' => 'Subfolder']
]
```

## Preparación para paquete Composer

El sistema está diseñado para ser fácilmente extraído a un paquete Composer independiente. Los archivos principales están organizados en:

- `app/Contracts/FileExplorerInterface.php`
- `app/Services/FileExplorers/`
- `app/Filament/Forms/Components/FileExplorer.php`
- `app/Livewire/FileExplorer.php`
- `resources/views/filament/forms/components/file-explorer.blade.php`
- `resources/views/livewire/file-explorer.blade.php`

## Compatibilidad hacia atrás

El sistema mantiene compatibilidad con el `GoogleDriveExplorer` original, que ahora extiende la nueva arquitectura sin cambios en la API pública.
