<?php

namespace App\Http\Responses;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;
use Filament\Http\Responses\Auth\LoginResponse as BaseLoginResponse;

class LoginResponse extends BaseLoginResponse
{
    public function toResponse($request): RedirectResponse|Redirector
    {
        /** @var User */
        $user = Auth::user();

        // Si no hay usuario autenticado o no tiene rol, redirigir al home
        if (!$user || !$user->hasrole('admin') && !$user->hasRole('cliente')) {
            return redirect()->route('home');
        }

        $redirectUrl = '/';

        // Determinar URL de redirección según el rol
        if ($user->hasRole('admin')) {
            $redirectUrl = '/admin';
        } elseif ($user->hasRole('cliente')) {
            $redirectUrl = '/app';
        }

        // Crear una vista temporal para redirección con JavaScript
        return redirect($redirectUrl);
        // return response()->view('dashboard-redirect', compact('redirectUrl'));
    }
}
