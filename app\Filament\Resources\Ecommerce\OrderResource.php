<?php

namespace App\Filament\Resources\Ecommerce;

use Dom\Text;
use Filament\Forms;

use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use LBCDev\Ecommerce\Models\Order;
use LBCDev\Ecommerce\Enums\Currency;
use LBCDev\Ecommerce\Models\Address;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use LBCDev\Ecommerce\Enums\OrderStatus;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\OrderResource\RelationManagers;
use App\Filament\Resources\Ecommerce\OrderResource\Pages\EditOrder;
use App\Filament\Resources\Ecommerce\OrderResource\Pages\ListOrders;
use App\Filament\Resources\Ecommerce\OrderResource\Pages\CreateOrder;
use App\Filament\Resources\Ecommerce\OrderResource\RelationManagers\ItemsRelationManager;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'E-commerce';
    protected static null|int $navigationSort = 6;
    protected static bool $shouldRegisterNavigation = true;
    protected static ?string $modelLabel = 'Orden';
    protected static ?string $pluralModelLabel = 'Ordenes';
    protected static ?string $navigationLabel = 'Ordenes';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('General')
                    ->schema([
                        TextInput::make('order_number')
                            ->required(),

                        Select::make('user_id')
                            ->label('Usuario')
                            ->options(function () {
                                $userModel = config('auth.providers.users.model');
                                return $userModel::all()->pluck('name', 'id');
                            })
                            ->default(fn($record) => $record?->user_id)
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set) {
                                $set('billing_address_id', null);
                                $set('shipping_address_id', null);
                            }),

                        Select::make('status')
                            ->options(OrderStatus::labels())
                            ->required(),

                        Select::make('currency')
                            ->options(Currency::labels())
                            ->required(),


                    ])
                    ->columns(2),

                Section::make('Importes')
                    ->schema([
                        TextInput::make('subtotal')
                            ->numeric()
                            ->minValue(0)
                            ->required(),

                        TextInput::make('tax_amount')
                            ->numeric()
                            ->minValue(0)
                            ->required(),
                        TextInput::make('total')
                            ->numeric()
                            ->minValue(0)
                            ->required(),
                    ])
                    ->columns(2),
                Section::make('Otra información')
                    ->schema([
                        Select::make('billing_address_id')
                            ->label('Dirección de facturación')
                            ->options(function (callable $get, ?\LBCDev\Ecommerce\Models\Order $record) {
                                $userId = $get('user_id') ?? $record?->user_id;

                                return Address::where('user_id', $userId)
                                    ->get()
                                    ->pluck('full_address', 'id');
                            })
                            ->searchable()
                            ->preload()
                            ->default(fn($record) => $record?->billing_address_id)
                            ->reactive(),

                        Select::make('shipping_address_id')
                            ->label('Dirección de envío')
                            ->options(function (callable $get, ?\LBCDev\Ecommerce\Models\Order $record) {
                                $userId = $get('user_id') ?? $record?->user_id;

                                return Address::where('user_id', $userId)
                                    ->get()
                                    ->pluck('full_address', 'id');
                            })
                            ->searchable()
                            ->preload()
                            ->default(fn($record) => $record?->shipping_address_id)
                            ->reactive(),
                        TextInput::make('notes'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('order_number')->sortable(),
                TextColumn::make('status')
                    ->badge()
                    // ->color(fn($state) => dd(OrderStatus::from($state)->color()))
                    ->sortable(),
                TextColumn::make('total')
                    ->money(function (Order $record) {
                        return $record->currency;
                    }, true, 'es')
                    ->sortable(),
                TextColumn::make(('user.name'))->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListOrders::route('/'),
            'create' => CreateOrder::route('/create'),
            'edit' => EditOrder::route('/{record}/edit'),
        ];
    }
}
