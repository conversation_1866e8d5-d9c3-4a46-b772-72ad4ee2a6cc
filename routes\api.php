<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\GoogleDriveController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Google Drive API routes
Route::middleware(['auth:web'])->prefix('google-drive')->name('google-drive.')->group(function () {
    Route::post('/list-files', [GoogleDriveController::class, 'listFiles'])->name('list-files');
    Route::post('/get-file', [GoogleDriveController::class, 'getFile'])->name('get-file');
    Route::post('/breadcrumb', [GoogleDriveController::class, 'getBreadcrumb'])->name('breadcrumb');
    Route::post('/search', [GoogleDriveController::class, 'searchFiles'])->name('search');
    Route::post('/test-connection', [GoogleDriveController::class, 'testConnection'])->name('test-connection');
});
