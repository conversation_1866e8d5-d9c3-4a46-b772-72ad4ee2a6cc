<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::create('payments', function (Blueprint $table) {
        //     $table->id();
        //     $table->foreignId('order_id')->constrained()->onDelete('cascade');
        //     $table->string('gateway', 50); // stripe, paypal, redsys, etc.
        //     $table->enum('estado', ['pendiente', 'procesando', 'exitoso', 'fallido', 'cancelado', 'reembolsado'])
        //         ->default('pendiente');
        //     $table->string('referencia')->nullable(); // ID/token de la pasarela
        //     $table->string('transaction_id')->nullable(); // ID de transacción de la pasarela
        //     $table->decimal('monto', 10, 2);
        //     $table->string('moneda', 3)->default('EUR');
        //     $table->text('raw_response')->nullable(); // Respuesta JSON completa del gateway
        //     $table->text('error_message')->nullable(); // Mensaje de error si falla
        //     $table->timestamp('paid_at')->nullable(); // Fecha de pago exitoso
        //     $table->timestamps();

        //     // Índices para optimizar consultas
        //     $table->index(['order_id', 'estado']);
        //     $table->index(['gateway', 'estado']);
        //     $table->index('referencia');
        //     $table->index('transaction_id');
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::dropIfExists('payments');
    }
};
