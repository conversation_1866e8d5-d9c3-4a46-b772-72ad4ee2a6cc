<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('nombre') && !$this->has('slug')) {
            $this->merge([
                'slug' => Str::slug($this->nombre),
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'nombre' => ['required', 'string', 'max:255'],
            'slug' => ['required', 'string', 'max:255', 'unique:products,slug'],
            'descripcion_corta' => ['nullable', 'string', 'max:500'],
            'descripcion_larga' => ['nullable', 'string'],
            'precio' => ['required', 'numeric', 'min:0'],
            'precio_descuento' => ['nullable', 'numeric', 'min:0'],
            'activo' => ['boolean'],
            'orden' => ['integer', 'min:0'],
            'categories' => ['nullable', 'array'],
            'categories.*' => ['exists:taxonomies,id'],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['exists:taxonomies,id'],
            'languages' => ['nullable', 'array'],
            'languages.*' => ['exists:languages,id'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'nombre' => 'nombre del producto',
            'slug' => 'slug del producto',
            'descripcion_corta' => 'descripción corta',
            'descripcion_larga' => 'descripción larga',
            'precio' => 'precio',
            'precio_descuento' => 'precio con descuento',
            'activo' => 'estado activo',
            'orden' => 'orden',
            'categories' => 'categorías',
            'categories.*' => 'categoría',
            'tags' => 'etiquetas',
            'tags.*' => 'etiqueta',
            'languages' => 'idiomas',
            'languages.*' => 'idioma',
        ];
    }
}
