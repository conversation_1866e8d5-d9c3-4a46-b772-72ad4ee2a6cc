<?php

namespace LBCDev\Ecommerce\Services;

use LBCDev\Ecommerce\Models\Cart;
use LBCDev\Ecommerce\Models\Order;
use LBCDev\Ecommerce\Events\OrderCreated;
use LBCDev\Ecommerce\Events\OrderStatusChanged;

class OrderService
{
    /**
     * Crear una orden desde un carrito.
     */
    public function createOrderFromCart(
        Cart $cart,
        array $customerData = [],
        ?int $userId = null
    ): Order {
        if (empty($cart->getItems())) {
            throw new \InvalidArgumentException('Cannot create order from empty cart');
        }

        // Validar datos del cliente
        $this->validateCustomerData($customerData);

        $order = Order::createFromCart($cart, $customerData, $userId);

        // Disparar evento
        event(new OrderCreated($order));

        return $order;
    }

    /**
     * Actualizar el estado de una orden.
     */
    public function updateOrderStatus(Order $order, string $newStatus): Order
    {
        $previousStatus = $order->status;
        $order->updateStatus($newStatus);

        // Disparar evento si el estado cambió
        if ($previousStatus !== $newStatus) {
            event(new OrderStatusChanged($order, $previousStatus, $newStatus));
        }

        return $order->fresh();
    }

    /**
     * Cancelar una orden.
     */
    public function cancelOrder(Order $order, string $reason = ''): Order
    {
        if (!$order->canBeCancelled()) {
            throw new \InvalidArgumentException(
                "Order {$order->order_number} cannot be cancelled. Current status: {$order->status}"
            );
        }

        return $this->updateOrderStatus($order, Order::STATUS_CANCELLED);
    }

    /**
     * Marcar orden como procesando.
     */
    public function processOrder(Order $order): Order
    {
        if ($order->status !== Order::STATUS_PENDING) {
            throw new \InvalidArgumentException(
                "Only pending orders can be processed. Current status: {$order->status}"
            );
        }

        return $this->updateOrderStatus($order, Order::STATUS_PROCESSING);
    }

    /**
     * Marcar orden como enviada.
     */
    public function shipOrder(Order $order, array $trackingInfo = []): Order
    {
        if (!in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_PROCESSING])) {
            throw new \InvalidArgumentException(
                "Order {$order->order_number} cannot be shipped. Current status: {$order->status}"
            );
        }

        return $this->updateOrderStatus($order, Order::STATUS_SHIPPED);
    }

    /**
     * Marcar orden como entregada.
     */
    public function deliverOrder(Order $order): Order
    {
        if ($order->status !== Order::STATUS_SHIPPED) {
            throw new \InvalidArgumentException(
                "Only shipped orders can be delivered. Current status: {$order->status}"
            );
        }

        return $this->updateOrderStatus($order, Order::STATUS_DELIVERED);
    }

    /**
     * Validar datos del cliente.
     */
    protected function validateCustomerData(array $customerData): void
    {
        $required = ['name', 'email'];

        foreach ($required as $field) {
            if (empty($customerData[$field])) {
                throw new \InvalidArgumentException("Customer {$field} is required");
            }
        }

        if (!filter_var($customerData['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException("Invalid email format");
        }
    }
}
