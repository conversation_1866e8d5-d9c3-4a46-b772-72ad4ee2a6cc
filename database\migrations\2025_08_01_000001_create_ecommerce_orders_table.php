<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use LBCDev\Ecommerce\Enums\Currency;
use LBCDev\Ecommerce\Enums\OrderStatus;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('lbcdev_ecommerce_orders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('order_number')->unique();
            $table->string('status')->default(OrderStatus::PENDING->value);

            // Totales
            $table->decimal('subtotal', 10, 2);
            $table->decimal('tax_amount', 10, 2);
            $table->decimal('total', 10, 2);
            $table->string('currency', 3)->default(Currency::EUR->value);

            // Direcciones
            $table->unsignedBigInteger('billing_address_id')->nullable();
            $table->unsignedBigInteger('shipping_address_id')->nullable();

            $table->foreign('billing_address_id')
                ->references('id')
                ->on('lbcdev_ecommerce_addresses')
                ->onDelete('set null');

            $table->foreign('shipping_address_id')
                ->references('id')
                ->on('lbcdev_ecommerce_addresses')
                ->onDelete('set null');

            // Notas
            $table->text('notes')->nullable();

            // Timestamps de estado
            $table->timestamp('shipped_at')->nullable();
            $table->timestamp('delivered_at')->nullable();

            $table->timestamps();

            // Índices
            $table->index('user_id');
            $table->index('status');
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('lbcdev_ecommerce_orders');
    }
};
