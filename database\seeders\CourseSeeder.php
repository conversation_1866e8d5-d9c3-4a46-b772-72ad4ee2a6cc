<?php

// database\seeders\CourseSeeder.php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // \App\Models\Course::create([
        //     'titulo' => 'Ascension Key',
        //     'descripcion' => 'The Ascension Key is a powerful tool for spiritual awakening and personal growth.',
        //     'slug' => 'ascension-key',
        //     'publicado' => true,
        // ]);

        \App\Models\Course::factory()
            ->withLessons(3)
            ->create([
                'titulo' => 'Quantum Leap Formula',
                'descripcion' => 'The Quantum Leap Formula is a powerful tool for manifestation and abundance.',
                'slug' => 'quantum-leap-formula',
                'publicado' => true,
            ]);

        \App\Models\Course::factory()
            ->withLessons(5)
            ->create([
                'titulo' => 'Ultimate Success Accelerator',
                'descripcion' => 'The Ultimate Success Accelerator is a powerful tool for manifestation and abundance.',
                'slug' => 'ultimate-success-accelerator',
                'publicado' => true,
            ]);
    }
}
