<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use LBCDev\Ecommerce\Models\Order;
use LBCDev\Ecommerce\Models\OrderItem;
use LBCDev\Ecommerce\Models\Product;

class OrderSeeder extends Seeder
{
    public function run(): void
    {
        Order::factory()->count(5)->create()->each(function ($order) {
            $order->items()->saveMany(
                Product::all()->random(3)->map(function ($product) use ($order) {
                    return new OrderItem([
                        'order_id' => $order->id,
                        'sellable_type' => Product::class,
                        'sellable_id' => $product->id,
                        'quantity' => rand(1, 5),
                        'unit_price' => $product->price,
                        'tax_rate' => $product->tax_rate,
                        'subtotal' => $product->price * rand(1, 5),
                        'tax_amount' => $product->price * rand(1, 5) * ($product->tax_rate / 100),
                        'total' => $product->price * rand(1, 5) * (1 + $product->tax_rate / 100),
                    ]);
                })
            );
        });
    }
}
