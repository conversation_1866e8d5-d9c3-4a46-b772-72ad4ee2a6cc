<?php

namespace Tests\Unit;

use App\Services\PaymentGatewayFactory;
use InvalidArgumentException;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PaymentGatewayFactoryTest extends TestCase
{
    use RefreshDatabase;

    protected PaymentGatewayFactory $factory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new PaymentGatewayFactory();
    }

    public function test_can_create_stripe_gateway_when_enabled(): void
    {
        // Configurar Stripe como habilitado
        config([
            'payments.gateways.stripe.enabled' => true,
            'payments.gateways.stripe.driver' => 'Stripe',
            'payments.gateways.stripe.options.apiKey' => 'sk_test_fake_key',
        ]);

        $gateway = $this->factory->create('stripe');

        $this->assertInstanceOf(\Omnipay\Common\GatewayInterface::class, $gateway);
        $this->assertEquals('Stripe Charge', $gateway->getName());
    }

    public function test_can_create_paypal_gateway_when_enabled(): void
    {
        // Configurar PayPal como habilitado
        config([
            'payments.gateways.paypal.enabled' => true,
            'payments.gateways.paypal.driver' => 'PayPal_Express',
            'payments.gateways.paypal.options.username' => 'test_username',
            'payments.gateways.paypal.options.password' => 'test_password',
            'payments.gateways.paypal.options.testMode' => true,
        ]);

        $gateway = $this->factory->create('paypal');

        $this->assertInstanceOf(\Omnipay\Common\GatewayInterface::class, $gateway);
        $this->assertEquals('PayPal Express', $gateway->getName());
    }

    public function test_throws_exception_for_disabled_gateway(): void
    {
        config([
            'payments.gateways.stripe.enabled' => false,
        ]);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Gateway 'stripe' is not enabled or configured.");

        $this->factory->create('stripe');
    }

    public function test_throws_exception_for_unconfigured_gateway(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Gateway 'nonexistent' is not enabled or configured.");

        $this->factory->create('nonexistent');
    }

    public function test_uses_default_gateway_when_none_specified(): void
    {
        config([
            'payments.default' => 'stripe',
            'payments.gateways.stripe.enabled' => true,
            'payments.gateways.stripe.driver' => 'Stripe',
            'payments.gateways.stripe.options.apiKey' => 'sk_test_fake_key',
        ]);

        $gateway = $this->factory->create();

        $this->assertInstanceOf(\Omnipay\Common\GatewayInterface::class, $gateway);
        $this->assertEquals('Stripe Charge', $gateway->getName());
    }

    public function test_get_enabled_gateways_returns_only_enabled(): void
    {
        config([
            'payments.gateways' => [
                'stripe' => ['enabled' => true],
                'paypal' => ['enabled' => false],
                'redsys' => ['enabled' => true],
            ]
        ]);

        $enabled = $this->factory->getEnabledGateways();

        $this->assertCount(2, $enabled);
        $this->assertArrayHasKey('stripe', $enabled);
        $this->assertArrayHasKey('redsys', $enabled);
        $this->assertArrayNotHasKey('paypal', $enabled);
    }

    public function test_is_gateway_enabled_returns_correct_status(): void
    {
        config([
            'payments.gateways.stripe.enabled' => true,
            'payments.gateways.paypal.enabled' => false,
        ]);

        $this->assertTrue($this->factory->isGatewayEnabled('stripe'));
        $this->assertFalse($this->factory->isGatewayEnabled('paypal'));
        $this->assertFalse($this->factory->isGatewayEnabled('nonexistent'));
    }

    public function test_get_gateway_name_returns_correct_names(): void
    {
        $this->assertEquals('Stripe', $this->factory->getGatewayName('stripe'));
        $this->assertEquals('PayPal', $this->factory->getGatewayName('paypal'));
        $this->assertEquals('Redsys', $this->factory->getGatewayName('redsys'));
        $this->assertEquals('Custom', $this->factory->getGatewayName('custom'));
    }

    public function test_get_supported_currencies_returns_correct_currencies(): void
    {
        $stripeCurrencies = $this->factory->getSupportedCurrencies('stripe');
        $paypalCurrencies = $this->factory->getSupportedCurrencies('paypal');
        $redsysCurrencies = $this->factory->getSupportedCurrencies('redsys');

        $this->assertContains('EUR', $stripeCurrencies);
        $this->assertContains('USD', $stripeCurrencies);
        $this->assertContains('EUR', $paypalCurrencies);
        $this->assertEquals(['EUR'], $redsysCurrencies);
    }
}
