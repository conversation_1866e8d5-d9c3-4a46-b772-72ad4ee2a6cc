<?php

// database\factories\LessonFactory.php

namespace Database\Factories;

use App\Models\Course;
use App\Models\Lesson;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Lesson>
 */
class LessonFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'course_id' => function () {
                return Course::factory()->create()->id;
            },
            'titulo' => fake()->sentence(),
            'contenido' => fake()->paragraph(),
            'slug' => fake()->slug(),
            'orden' => function (array $attributes) {
                return Lesson::where('course_id', $attributes['course_id'])->count() + 1;
            },
            'publicado' => fake()->boolean(),
        ];
    }
}
