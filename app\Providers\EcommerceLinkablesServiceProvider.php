<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use LBCDev\Ecommerce\Facades\Ecommerce;
use App\Models\File;
use App\Models\Course;

class EcommerceLinkablesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Registrar File como tipo linkable
        Ecommerce::registerLinkable(File::class, [
            'label' => 'Archivo',
            'icon' => 'heroicon-o-document',
            'allowed_purposes' => ['primary', 'included', 'bonus'],
            'default_purpose' => 'included',
            'default_access_policy' => 'after_payment',
        ]);

        // Registrar Course como tipo linkable
        Ecommerce::registerLinkable(Course::class, [
            'label' => 'Curso',
            'icon' => 'heroicon-o-academic-cap',
            'allowed_purposes' => ['primary', 'grants_access'],
            'default_purpose' => 'grants_access',
            'default_access_policy' => 'after_payment',
        ]);
    }
}
