<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use LBCDev\Ecommerce\Database\Factories\AddressFactory;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::factory()
            ->withAddress(2)
            ->create([
                'name' => 'Admin',
                'email' => env('ADMIN_EMAIL', '<EMAIL>'),
                'password' => bcrypt(env('ADMIN_PASSWORD', 'password1234567890')),
            ])
            ->assignRole('admin');

        User::factory()
            ->withAddress(2)
            ->create([
                'name' => 'Cliente',
                'email' => env('CLIENT_EMAIL', '<EMAIL>'),
                'password' => bcrypt(env('CLIENT_PASSWORD', 'password1234567890')),
            ])
            ->assignRole('cliente');
    }
}
