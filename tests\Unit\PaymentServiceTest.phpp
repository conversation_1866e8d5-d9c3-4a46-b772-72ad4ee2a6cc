<?php

namespace Tests\Unit;

use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Services\PaymentService;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PaymentServiceTest extends TestCase
{
    use RefreshDatabase;

    protected PaymentService $paymentService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->paymentService = $this->app->make('payment.service');
    }

    public function test_can_create_payment_for_order(): void
    {
        $user = User::factory()->create();
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 100.00,
            'estado' => 'pendiente',
            'moneda' => 'EUR',
        ]);

        config(['payments.default' => 'stripe']);

        $payment = $this->paymentService->createPayment($order, 'stripe');

        $this->assertInstanceOf(Payment::class, $payment);
        $this->assertEquals($order->id, $payment->order_id);
        $this->assertEquals('stripe', $payment->gateway);
        $this->assertEquals(Payment::ESTADO_PENDIENTE, $payment->estado);
        $this->assertEquals(100.00, $payment->monto);
        $this->assertEquals('EUR', $payment->moneda);
    }

    public function test_create_payment_uses_default_gateway_when_none_specified(): void
    {
        $user = User::factory()->create();
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 50.00,
            'estado' => 'pendiente',
            'moneda' => 'EUR',
        ]);

        config(['payments.default' => 'paypal']);

        $payment = $this->paymentService->createPayment($order);

        $this->assertEquals('paypal', $payment->gateway);
    }

    public function test_get_available_gateways_returns_array(): void
    {
        $result = $this->paymentService->getAvailableGateways();

        $this->assertIsArray($result);
    }

    public function test_verify_webhook_signature_for_stripe(): void
    {
        config(['payments.gateways.stripe.webhook_secret' => 'test_secret']);

        $payload = 'test_payload';
        $expectedSignature = hash_hmac('sha256', $payload, 'test_secret');

        $result = $this->paymentService->verifyWebhookSignature(
            $payload,
            $expectedSignature,
            'stripe'
        );

        $this->assertTrue($result);
    }

    public function test_verify_webhook_signature_fails_with_wrong_signature(): void
    {
        config(['payments.gateways.stripe.webhook_secret' => 'test_secret']);

        $result = $this->paymentService->verifyWebhookSignature(
            'test_payload',
            'wrong_signature',
            'stripe'
        );

        $this->assertFalse($result);
    }

    public function test_verify_webhook_signature_fails_without_secret(): void
    {
        config(['payments.gateways.stripe.webhook_secret' => null]);

        $result = $this->paymentService->verifyWebhookSignature(
            'test_payload',
            'any_signature',
            'stripe'
        );

        $this->assertFalse($result);
    }

    public function test_verify_webhook_signature_returns_true_for_non_stripe_gateways(): void
    {
        $result = $this->paymentService->verifyWebhookSignature(
            'test_payload',
            'any_signature',
            'paypal'
        );

        $this->assertTrue($result);
    }
}
