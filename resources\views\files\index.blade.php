@extends('layouts.landing')

@section('content')
    {{-- @php
        $menu = \App\Models\Menu::where('name', 'principal')->first();
    @endphp

    @if($menu)
        <ul>
            @foreach($menu->items()->whereNull('parent_id')->get() as $item)
                <li><a href="{{ $item->url }}">{{ $item->label }}</a></li>
            @endforeach
        </ul>
    @endif --}}

    {{-- Lista con todos los files, con nombre y link --}}
    <div class="container px-auto">
        <x-navbar
            brand="Quantum Transition"
            class="px-4 md:px-20 shadow-lg bg-black fixed top-0 z-50"
            :links="[
                ['label' => 'Inicio', 'href' => route('home')],
                ['label' => 'Cursos', 'href' => route('products.index')],
                ['label' => 'Sobre Mi', 'href' => '#contacto'],
                ['label' => 'Filess', 'href' => route('files.index')]
            ]"/>
    </div>

    <div class="container px-auto mt-10">
        <h1 class="text-3xl font-bold text-gray-900">Archivos</h1>
        <ul>
            @foreach($files as $file)
                <li>
                    <a href="{{ route('files.download', $file) }}" target="_blank">
                        {{ $file->nombre }} - {{ $file->url }}
                    </a>
                </li>
            @endforeach
        </ul>
    </div>

@endsection

