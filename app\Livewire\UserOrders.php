<?php

namespace App\Livewire;

use LBCDev\Ecommerce\Models\Order;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class UserOrders extends Component
{
    use WithPagination;

    public $selectedOrder = null;
    public $showOrderDetails = false;
    public $filterStatus = '';

    public function mount()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }
    }

    public function showDetails($orderId)
    {
        $this->selectedOrder = Order::where('id', $orderId)
            ->where('user_id', Auth::id())
            ->with(['items.product', 'payments'])
            ->first();

        if ($this->selectedOrder) {
            $this->showOrderDetails = true;
        }
    }

    public function closeDetails()
    {
        $this->showOrderDetails = false;
        $this->selectedOrder = null;
    }

    public function filterByStatus($status)
    {
        $this->filterStatus = $status;
        $this->resetPage();
    }

    public function getOrdersProperty()
    {
        return Order::where('user_id', Auth::id())
            ->when($this->filterStatus, function ($query) {
                $query->where('estado', $this->filterStatus);
            })
            ->with(['items.product', 'payments'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);
    }

    public function getStatusBadgeClass($status)
    {
        return match ($status) {
            'pendiente' => 'bg-yellow-100 text-yellow-800',
            'pagado' => 'bg-green-100 text-green-800',
            'fallido' => 'bg-red-100 text-red-800',
            'cancelado' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    public function getPaymentStatusBadgeClass($status)
    {
        return match ($status) {
            'pendiente' => 'bg-yellow-100 text-yellow-800',
            'procesando' => 'bg-blue-100 text-blue-800',
            'exitoso' => 'bg-green-100 text-green-800',
            'fallido' => 'bg-red-100 text-red-800',
            'cancelado' => 'bg-gray-100 text-gray-800',
            'reembolsado' => 'bg-purple-100 text-purple-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    public function render()
    {
        return view('livewire.user-orders', [
            'orders' => $this->orders,
        ]);
    }
}
