# LBCDev Ecommerce Package - API Reference

## Package Information
- **Name**: `lbcdev/ecommerce`
- **Namespace**: `LBCDev\Ecommerce`
- **Laravel Compatibility**: ^10.0|^11.0|^12.0
- **PHP**: ^8.1

## Service Providers

### EcommerceServiceProvider
Main service provider that bootstraps the package.

**Location**: `src/EcommerceServiceProvider.php`

**Features**:
- Registers `CartServiceProvider`
- Loads routes, migrations, views, translations
- Publishes config, views, and assets
- Sets up event listeners for user login
- Configures automatic cart cleanup scheduling

**Published Assets**:
```bash
php artisan vendor:publish --tag=ecommerce-config
php artisan vendor:publish --tag=ecommerce-views  
php artisan vendor:publish --tag=ecommerce-assets
```

### CartServiceProvider
Manages cart-related services and commands.

**Location**: `src/Providers/CartServiceProvider.php`

**Services**:
- Registers `CartService` as singleton
- Aliases `CartService` as `ecommerce.cart`
- Registers cleanup commands for console

## Core Services

### CartService
**Alias**: `ecommerce.cart`
**Binding**: Singleton

Main service for cart management functionality.

## Console Commands

### CleanupExpiredCarts
**Class**: `LBCDev\Ecommerce\Console\Commands\CleanupExpiredCarts`
**Schedule**: Daily (if auto_cleanup enabled)

Cleans up expired cart sessions and data.

## Event System

### Events
- **UserLoggedIn**: `LBCDev\Ecommerce\Events\UserLoggedIn`
  - Triggered on `Illuminate\Auth\Events\Login`

## Configuration

### Config File: `config/ecommerce.php`
```php
return [
    'cart' => [
        'auto_cleanup' => true, // Enable automatic cart cleanup
        // Additional cart configuration...
    ],
    // Other ecommerce settings...
];
```

## File Structure
```
src/
├── EcommerceServiceProvider.php
├── Providers/
│   └── CartServiceProvider.php
├── Console/
│   └── Commands/
│       └── CleanupExpiredCarts.php
├── Events/
│   └── UserLoggedIn.php
└── Services/
    └── CartService.php

config/
└── ecommerce.php

resources/
├── views/
└── lang/

routes/
└── web.php

database/
└── migrations/

public/
└── (assets)
```

## Usage Examples

### Accessing Cart Service
```php
// Via Service Container
$cartService = app(CartService::class);

// Via Alias
$cartService = app('ecommerce.cart');

// Via Dependency Injection
public function __construct(CartService $cartService)
{
    $this->cartService = $cartService;
}
```

### Configuration Access
```php
$autoCleanup = config('ecommerce.cart.auto_cleanup');
```

### Publishing Assets
```bash
# Publish configuration
php artisan vendor:publish --provider="LBCDev\Ecommerce\EcommerceServiceProvider" --tag="ecommerce-config"

# Publish views
php artisan vendor:publish --provider="LBCDev\Ecommerce\EcommerceServiceProvider" --tag="ecommerce-views"

# Publish public assets
php artisan vendor:publish --provider="LBCDev\Ecommerce\EcommerceServiceProvider" --tag="ecommerce-assets"
```

## Integration Notes

### Auto-Discovery
The package is auto-discovered by Laravel through the `extra.laravel.providers` configuration in `composer.json`.

### Dependencies
- **illuminate/support**: ^10.0|^11.0|^12.0
- Compatible with Laravel's Context system (Laravel 12+)
- Integrates with Laravel's event system
- Uses Laravel's scheduling system for cleanup tasks

### View Namespace
Views are registered under the `ecommerce` namespace:
```php
return view('ecommerce::cart.index');
```

### Translation Namespace  
Translations are available under the `ecommerce` namespace:
```php
__('ecommerce::messages.cart_updated')
```

## Development & Testing

### Test Setup
```php
// In tests, use Orchestra Testbench
protected function getPackageProviders($app)
{
    return [
        \LBCDev\Ecommerce\EcommerceServiceProvider::class,
    ];
}
```

### Service Resolution
The package follows Laravel's service container patterns and can be extended or overridden through standard Laravel mechanisms.