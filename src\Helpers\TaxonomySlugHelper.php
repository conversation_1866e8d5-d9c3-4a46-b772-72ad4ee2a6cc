<?php

namespace LBCDev\Ecommerce\Helpers;

class TaxonomySlugHelper
{
    public static function withPrefix(string $slug, string $type): string
    {
        $prefixes = config('lbcdev-ecommerce.taxonomy_slug_prefixes', []);

        return ($prefixes[$type] ?? '') . $slug;
    }

    public static function stripPrefix(string $slug, string $type): string
    {
        $prefixes = config('lbcdev-ecommerce.taxonomy_slug_prefixes', []);

        $prefix = $prefixes[$type] ?? '';
        return str_starts_with($slug, $prefix) ? substr($slug, strlen($prefix)) : $slug;
    }
}
