<?php

namespace LBCDev\Ecommerce\Database\Factories;

use LBCDev\Ecommerce\Enums\Currency;
use LBCDev\Ecommerce\Enums\OrderStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Foundation\Auth\User;

class OrderFactory extends Factory
{
    protected $model = \LBCDev\Ecommerce\Models\Order::class;

    public function definition(): array
    {
        $subtotal = $this->faker->randomFloat(2, 10, 1000);
        $taxAmount = $this->faker->randomFloat(2, 1, 100);
        $total = $subtotal + $taxAmount;

        $user = User::all()->random();
        if (!$user || !method_exists($user, 'addresses')) {
            $user = null;
            $billingAddress = null;
            $shippingAddress = null;
        } else {
            $billingAddress = $user->addresses()->first()?->id;
            $shippingAddress = $user->addresses()->first()?->id;
        }

        return [
            'user_id' => $user?->id,
            'status' => $this->faker->randomElement(OrderStatus::getPossibleValues()),
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total' => $total,
            'currency' => $this->faker->randomElement(Currency::getPossibleValues()),
            'billing_address_id' => $billingAddress,
            'shipping_address_id' => $shippingAddress,
            'notes' => null,
            'shipped_at' => null,
            'delivered_at' => null,
        ];
    }

    public function withItems(int $count = 3)
    {
        return $this->has(OrderItemFactory::new()->count($count));
    }
}
