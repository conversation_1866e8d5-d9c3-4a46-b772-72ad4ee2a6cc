<?php

namespace LBCDev\Ecommerce\Traits;

use Illuminate\Support\Collection;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomy;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasTags
{
    use HasTaxonomy;

    /**
     * Tipo de taxonomía para etiquetas (configurable).
     */
    protected function getTagTaxonomyType(): string
    {
        return config('lbcdev-ecommerce.tags_taxonomy_type', 'lbcdev-ecommerce-tag');
    }

    /**
     * Devuelve las etiquetas asociadas al modelo.
     */
    public function tags(): Collection
    {
        return $this->taxonomiesOfType($this->getTagTaxonomyType());
    }

    /**
     * Asocia una o varias etiquetas al modelo.
     */
    public function addTag($tags): self
    {
        return $this->attachTaxonomies($tags);
    }

    /**
     * Elimina una o varias etiquetas del modelo.
     */
    public function removeTag($tags = null): self
    {
        return $this->detachTaxonomies($tags);
    }

    /**
     * Reemplaza todas las etiquetas existentes por las nuevas.
     */
    public function syncTags($tags): self
    {
        $this->tagsRelation()->each(function ($tag) {
            $this->tagsRelation()->detach($tag);
        });

        // Detectar si $tags es Collection o array para recorrer
        if ($tags instanceof Collection) {
            $tags = $tags->all();
        }
        if (!is_array($tags)) {
            $tags = [$tags];
        }

        foreach ($tags as $tag) {
            $this->tagsRelation()->attach($tag);
        }

        return $this;
    }

    /**
     * Verifica si el modelo tiene una o varias etiquetas.
     */
    public function hasTag($tags): bool
    {
        return $this->hasTaxonomies($tags);
    }

    /**
     * Relacion polimórfica con las etiquetas.
     */
    public function tagsRelation(): MorphToMany
    {
        return $this->morphToMany(
            Taxonomy::class,
            'taxonomable',
            'taxonomables',
            'taxonomable_id',
            'taxonomy_id'
        )->where('type', $this->getTagTaxonomyType());
    }
}
