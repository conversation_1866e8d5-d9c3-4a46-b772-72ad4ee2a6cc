<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Str;

class PageBuilder extends Component
{
    public $blocks = [];

    public function mount($initialContent = [])
    {
        // Si el contenido viene como string (por ejemplo, JSON), decodificarlo
        $this->blocks = is_string($initialContent)
            ? json_decode($initialContent, true)
            : ($initialContent ?? []);
    }

    public function updatedBlocks()
    {
        $this->emit('updateFieldState', $this->blocks); // Emitir a Filament
    }

    public function addBlock($type)
    {
        $this->blocks[] = [
            'uuid' => Str::uuid(),
            'type' => $type,
            'data' => [],
        ];

        $this->updatedBlocks();
    }

    public function removeBlock($uuid)
    {
        $this->blocks = array_values(array_filter($this->blocks, fn($block) => $block['uuid'] !== $uuid));
        $this->updatedBlocks();
    }

    public function moveBlockUp($index)
    {
        if ($index > 0) {
            [$this->blocks[$index - 1], $this->blocks[$index]] = [$this->blocks[$index], $this->blocks[$index - 1]];
        }
        $this->updatedBlocks();
    }

    public function moveBlockDown($index)
    {
        if ($index < count($this->blocks) - 1) {
            [$this->blocks[$index + 1], $this->blocks[$index]] = [$this->blocks[$index], $this->blocks[$index + 1]];
        }
        $this->updatedBlocks();
    }

    public function render()
    {
        if (!is_array($this->blocks)) {
            $this->blocks = [];
        }

        return view('livewire.page-builder', [
            'blockViews' => [
                'text' => 'blocks.text',
                'image' => 'blocks.image',
                'button' => 'blocks.button',
            ],
        ]);
    }
}
