<?php

namespace App\Filament\Resources\Ecommerce\ProductResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\File;
use App\Models\Course;
use LBCDev\Ecommerce\Facades\Ecommerce;

class LinkablesRelationManager extends RelationManager
{
    protected static string $relationship = 'linkables';

    protected static ?string $title = 'Elementos Vinculados';

    protected static ?string $modelLabel = 'elemento';

    protected static ?string $pluralModelLabel = 'elementos';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('productable_type')
                    ->label('Tipo de Elemento')
                    ->options(function () {
                        $linkables = Ecommerce::getLinkablesForSelect();
                        return $linkables->pluck('label', 'value')->toArray();
                    })
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn (callable $set) => $set('productable_id', null)),
                
                Forms\Components\Select::make('productable_id')
                    ->label('Elemento')
                    ->options(function (callable $get) {
                        $type = $get('productable_type');
                        if (!$type) {
                            return [];
                        }
                        
                        return match ($type) {
                            File::class => File::all()->pluck('nombre', 'id'),
                            Course::class => Course::all()->pluck('titulo', 'id'),
                            default => [],
                        };
                    })
                    ->required()
                    ->searchable(),
                
                Forms\Components\Select::make('purpose')
                    ->label('Propósito')
                    ->options(function (callable $get) {
                        $type = $get('productable_type');
                        if (!$type) {
                            return [
                                'primary' => 'Principal',
                                'included' => 'Incluido',
                                'bonus' => 'Bonus',
                                'grants_access' => 'Otorga Acceso',
                            ];
                        }
                        
                        $allowedPurposes = Ecommerce::getAllowedPurposes($type);
                        $purposeLabels = [
                            'primary' => 'Principal',
                            'included' => 'Incluido',
                            'bonus' => 'Bonus',
                            'grants_access' => 'Otorga Acceso',
                        ];
                        
                        return collect($allowedPurposes)
                            ->mapWithKeys(fn ($purpose) => [$purpose => $purposeLabels[$purpose] ?? $purpose])
                            ->toArray();
                    })
                    ->default(function (callable $get) {
                        $type = $get('productable_type');
                        return $type ? Ecommerce::getDefaultPurpose($type) : 'included';
                    })
                    ->required()
                    ->reactive(),
                
                Forms\Components\Select::make('access_policy')
                    ->label('Política de Acceso')
                    ->options([
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                    ])
                    ->default(function (callable $get) {
                        $type = $get('productable_type');
                        return $type ? Ecommerce::getDefaultAccessPolicy($type) : 'after_payment';
                    })
                    ->required(),
                
                Forms\Components\KeyValue::make('meta')
                    ->label('Metadatos')
                    ->keyLabel('Clave')
                    ->valueLabel('Valor')
                    ->addActionLabel('Añadir metadato')
                    ->helperText('Configuración adicional específica para este elemento.'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('productable_type')
            ->columns([
                Tables\Columns\TextColumn::make('productable_type')
                    ->label('Tipo')
                    ->badge()
                    ->color('primary')
                    ->formatStateUsing(function (string $state): string {
                        $linkable = Ecommerce::getLinkable($state);
                        return $linkable['label'] ?? class_basename($state);
                    }),
                
                Tables\Columns\TextColumn::make('productable.nombre')
                    ->label('Nombre')
                    ->getStateUsing(function ($record) {
                        $productable = $record->productable;
                        if (!$productable) return 'N/A';
                        
                        return match (get_class($productable)) {
                            File::class => $productable->nombre,
                            Course::class => $productable->titulo,
                            default => $productable->name ?? $productable->title ?? 'Sin nombre',
                        };
                    })
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('purpose')
                    ->label('Propósito')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'primary' => 'primary',
                        'included' => 'success',
                        'bonus' => 'warning',
                        'grants_access' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'primary' => 'Principal',
                        'included' => 'Incluido',
                        'bonus' => 'Bonus',
                        'grants_access' => 'Otorga Acceso',
                        default => $state,
                    }),
                
                Tables\Columns\TextColumn::make('access_policy')
                    ->label('Política de Acceso')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'immediate' => 'success',
                        'after_payment' => 'warning',
                        'scheduled' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                        default => $state,
                    }),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Vinculado el')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('productable_type')
                    ->label('Tipo de Elemento')
                    ->options(function () {
                        $linkables = Ecommerce::getLinkablesForSelect();
                        return $linkables->pluck('label', 'value')->toArray();
                    }),
                
                Tables\Filters\SelectFilter::make('purpose')
                    ->label('Propósito')
                    ->options([
                        'primary' => 'Principal',
                        'included' => 'Incluido',
                        'bonus' => 'Bonus',
                        'grants_access' => 'Otorga Acceso',
                    ]),
                
                Tables\Filters\SelectFilter::make('access_policy')
                    ->label('Política de Acceso')
                    ->options([
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
