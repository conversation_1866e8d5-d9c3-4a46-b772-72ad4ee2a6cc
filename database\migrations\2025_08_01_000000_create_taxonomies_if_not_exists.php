<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        $table = config('lbcdev-ecommerce.categories_table', 'taxonomies');

        if (!Schema::hasTable($table)) {
            Schema::create($table, function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('slug')->unique();
                $table->string('type')->index();
                $table->foreignId('parent_id')->nullable()->constrained($table);
                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        $table = config('lbcdev-ecommerce.categories_table', 'taxonomies');

        if (Schema::hasTable($table)) {
            Schema::dropIfExists($table);
        }
    }
};
