<?php

namespace LBCDev\Ecommerce\Console\Commands;

use LBCDev\Ecommerce\Models\PersistentCart;
use Illuminate\Console\Command;

class CleanupExpiredCarts extends Command
{
    protected $signature = 'ecommerce:cleanup-carts';
    protected $description = 'Clean up expired carts from database';

    public function handle(): int
    {
        $this->info('Cleaning up expired carts...');

        $deletedCount = PersistentCart::expired()->count();
        PersistentCart::expired()->delete();

        $this->info("Deleted {$deletedCount} expired carts.");

        return Command::SUCCESS;
    }
}
