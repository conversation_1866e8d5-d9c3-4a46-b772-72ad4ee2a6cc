<?php

namespace LBCDev\Ecommerce\Models;

use LBCDev\Ecommerce\Traits\HasTags;
use LBCDev\Ecommerce\Traits\Sellable;
use Illuminate\Database\Eloquent\Model;
use LBCDev\Ecommerce\Traits\HasCategories;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use LBCDev\Ecommerce\Database\Factories\ProductFactory;


class Product extends Model
{
    use Sellable;
    use HasCategories;
    use HasTags;
    use HasFactory;

    protected $table = 'lbcdev_ecommerce_products';

    protected $fillable = [
        'name',
        'description',
        'price',
        'tax_rate',
        'is_active',
        'price_discount',
        'stock',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function variants()
    {
        return $this->hasMany(ProductVariant::class, 'product_id');
    }

    /**
     * Obtener todos los linkables asociados al producto
     */
    public function linkables()
    {
        return $this->hasMany(ProductLinkable::class, 'product_id');
    }

    /**
     * Obtener linkables de un tipo específico
     */
    public function linkablesOfType(string $type)
    {
        return $this->linkables()->where('productable_type', $type);
    }

    /**
     * Obtener archivos vinculados
     */
    // public function files()
    // {
    //     return $this->linkablesOfType('App\\Models\\File')
    //         ->with('productable')
    //         ->get()
    //         ->pluck('productable');
    // }

    // /**
    //  * Obtener cursos vinculados
    //  */
    // public function courses()
    // {
    //     return $this->linkablesOfType('App\\Models\\Course')
    //         ->with('productable')
    //         ->get()
    //         ->pluck('productable');
    // }

    /**
     * Vincular un modelo al producto
     */
    public function linkModel($model, array $attributes = [])
    {
        $defaultAttributes = [
            'purpose' => 'included',
            'access_policy' => 'after_payment',
            'meta' => null,
        ];

        $attributes = array_merge($defaultAttributes, $attributes);

        return $this->linkables()->create([
            'productable_type' => get_class($model),
            'productable_id' => $model->getKey(),
            'purpose' => $attributes['purpose'],
            'access_policy' => $attributes['access_policy'],
            'meta' => $attributes['meta'],
        ]);
    }

    public function getCategoriesAttribute()
    {
        return $this->categoriesRelation;
    }

    public function getTagsAttribute()
    {
        return $this->tagsRelation;
    }

    public function getPriceWithDiscountAttribute()
    {
        return $this->price - $this->price_discount;
    }

    protected static function newFactory()
    {
        return ProductFactory::new();
    }
}
