<?php

namespace App\Filament\Resources\FileResource\Pages;

use App\Filament\Resources\FileResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateFile extends CreateRecord
{
    protected static string $resource = FileResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $data['metadata'] = $data['url'];
        $data['url'] = $data['url']['url'];
        return static::getModel()::create($data);
    }
}
