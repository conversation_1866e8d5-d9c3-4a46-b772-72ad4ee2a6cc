<?php

namespace App\Filament\Forms\Components;

use Filament\Forms\Components\Field;
use Illuminate\View\ComponentAttributeBag;

class PageBuilderField extends Field
{
    protected string $view = 'filament.forms.components.page-builder-field';

    protected function setUp(): void
    {
        parent::setUp();

        // Define cómo se guarda el valor
        $this->dehydrateStateUsing(static function ($state) {
            return $state;
        });

        $this->afterStateHydrated(function (PageBuilderField $component, $state) {
            // Si necesitas transformar el JSON al cargar, hazlo aquí
            $component->state($state ?? []);
        });
    }
}
