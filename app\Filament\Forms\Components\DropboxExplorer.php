<?php

namespace App\Filament\Forms\Components;

use Filament\Forms\Components\Field;
use Filament\Forms\Components\Concerns\HasPlaceholder;

class DropboxExplorer extends Field
{
    use HasPlaceholder;

    protected string $view = 'filament.forms.components.dropbox-explorer';

    protected ?string $oauthServiceField = null;
    protected bool $showSelectedFileInfo = true;
    protected bool $allowFolderSelection = false;
    protected array $acceptedMimeTypes = [];

    public function oauthServiceField(string $fieldName): static
    {
        $this->oauthServiceField = $fieldName;
        return $this;
    }

    public function getOauthServiceField(): ?string
    {
        return $this->oauthServiceField;
    }

    public function showSelectedFileInfo(bool $show = true): static
    {
        $this->showSelectedFileInfo = $show;
        return $this;
    }

    public function getShowSelectedFileInfo(): bool
    {
        return $this->showSelectedFileInfo;
    }

    public function allowFolderSelection(bool $allow = true): static
    {
        $this->allowFolderSelection = $allow;
        return $this;
    }

    public function getAllowFolderSelection(): bool
    {
        return $this->allowFolderSelection;
    }

    public function acceptedMimeTypes(array $mimeTypes): static
    {
        $this->acceptedMimeTypes = $mimeTypes;
        return $this;
    }

    public function getAcceptedMimeTypes(): array
    {
        return $this->acceptedMimeTypes;
    }

    public function acceptImages(): static
    {
        return $this->acceptedMimeTypes([
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml'
        ]);
    }

    public function acceptDocuments(): static
    {
        return $this->acceptedMimeTypes([
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv'
        ]);
    }

    public function acceptVideos(): static
    {
        return $this->acceptedMimeTypes([
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/flv',
            'video/webm',
            'video/mkv'
        ]);
    }

    public function acceptAudio(): static
    {
        return $this->acceptedMimeTypes([
            'audio/mp3',
            'audio/wav',
            'audio/ogg',
            'audio/aac',
            'audio/flac',
            'audio/wma'
        ]);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->default('');

        $this->dehydrateStateUsing(function ($state) {
            return $state;
        });
    }
}
