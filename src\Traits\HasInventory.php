<?php

namespace App\Traits;

trait HasInventory
{
    public function hasStock(int $quantity = 1): bool
    {
        return $this->stock >= $quantity;
    }

    public function reduceStock(int $quantity): void
    {
        if ($this->hasStock($quantity)) {
            $this->decrement('stock', $quantity);
        } else {
            throw new \RuntimeException("Not enough stock");
        }
    }

    public function increaseStock(int $quantity): void
    {
        $this->increment('stock', $quantity);
    }
}
