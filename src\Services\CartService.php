<?php

namespace LBCDev\Ecommerce\Services;

use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Session\Session;
use LBCDev\Ecommerce\Contracts\CartStorageInterface;
use LBCDev\Ecommerce\Models\Cart;
use LBCDev\Ecommerce\Services\CartStorage\HybridCartStorage;

class CartService
{
    protected CartStorageInterface $storage;
    protected Session $session;
    protected AuthManager $auth;

    public function __construct(
        CartStorageInterface $storage,
        Session $session,
        AuthManager $auth
    ) {
        $this->storage = $storage;
        $this->session = $session;
        $this->auth = $auth;
    }

    public function getCart(): Cart
    {
        return $this->storage->getCart($this->getCartIdentifier());
    }

    public function saveCart(Cart $cart): void
    {
        $this->storage->saveCart($this->getCartIdentifier(), $cart);
    }

    public function clearCart(): void
    {
        $this->storage->clearCart($this->getCartIdentifier());
    }

    public function addItem($sellable, int $quantity = 1): Cart
    {
        $cart = $this->getCart();

        $existingItem = $cart->findItem($sellable);
        if ($existingItem) {
            $cart->updateQuantity($sellable, $existingItem->getQuantity() + $quantity);
        } else {
            $cart->addItem($sellable, $quantity);
        }

        $this->saveCart($cart);
        return $cart;
    }

    public function removeItem($sellable): Cart
    {
        $cart = $this->getCart();
        $cart->removeItem($sellable);
        $this->saveCart($cart);

        return $cart;
    }

    public function updateQuantity($sellable, int $quantity): Cart
    {
        $cart = $this->getCart();
        $cart->updateQuantity($sellable, $quantity);
        $this->saveCart($cart);

        return $cart;
    }

    public function handleUserLogin($user): Cart
    {
        if (!($this->storage instanceof HybridCartStorage)) {
            return $this->getCart();
        }

        $sessionIdentifier = $this->getSessionIdentifier();
        $userIdentifier = $this->getUserIdentifier($user);

        return $this->storage->migrateSessionToUser($sessionIdentifier, $userIdentifier);
    }

    protected function getCartIdentifier(): string
    {
        $user = $this->getAuthenticatedUser();
        if ($user) {
            return $this->getUserIdentifier($user);
        }

        return $this->getSessionIdentifier();
    }

    protected function getUserIdentifier($user): string
    {
        return 'user_' . $user->getAuthIdentifier();
    }

    protected function getSessionIdentifier(): string
    {
        return 'session_' . $this->session->getId();
    }

    protected function getAuthenticatedUser(): ?\Illuminate\Contracts\Auth\Authenticatable
    {
        foreach (array_keys(config('auth.guards')) as $guard) {
            $user = $this->auth->guard($guard)->user();
            if ($user) {
                return $user;
            }
        }

        return null;
    }
}
