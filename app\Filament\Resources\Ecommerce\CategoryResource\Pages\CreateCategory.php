<?php

namespace App\Filament\Resources\Ecommerce\CategoryResource\Pages;

use Filament\Actions;
use LBCDev\Ecommerce\Models\Category;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\Ecommerce\CategoryResource;

class CreateCategory extends CreateRecord
{
    protected static string $resource = CategoryResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Si no hay parent id hay que añadir la raíz
        $data['parent_id'] ??= Category::root()->id;


        return Category::create($data);
    }
}
