<?php

namespace LBCDev\Ecommerce\Facades;

use Illuminate\Support\Facades\Facade;
use LBCDev\Ecommerce\Services\LinkableRegistry;

/**
 * @method static void registerLinkable(string $modelClass, array $config = [])
 * @method static array getAllLinkables()
 * @method static array|null getLinkable(string $modelClass)
 * @method static bool isLinkableRegistered(string $modelClass)
 * @method static \Illuminate\Support\Collection getLinkablesForSelect()
 * @method static array getAllowedPurposes(string $modelClass)
 * @method static string getDefaultPurpose(string $modelClass)
 * @method static string getDefaultAccessPolicy(string $modelClass)
 */
class Ecommerce extends Facade
{
    protected static function getFacadeAccessor()
    {
        return 'ecommerce';
    }

    /**
     * Registrar un tipo linkable
     */
    public static function registerLinkable(string $modelClass, array $config = []): void
    {
        LinkableRegistry::register($modelClass, $config);
    }

    /**
     * Obtener todos los tipos linkables
     */
    public static function getAllLinkables(): array
    {
        return LinkableRegistry::all();
    }

    /**
     * Obtener configuración de un tipo específico
     */
    public static function getLinkable(string $modelClass): ?array
    {
        return LinkableRegistry::get($modelClass);
    }

    /**
     * Verificar si un tipo está registrado
     */
    public static function isLinkableRegistered(string $modelClass): bool
    {
        return LinkableRegistry::isRegistered($modelClass);
    }

    /**
     * Obtener tipos para select
     */
    public static function getLinkablesForSelect(): \Illuminate\Support\Collection
    {
        return LinkableRegistry::getForSelect();
    }

    /**
     * Obtener propósitos permitidos
     */
    public static function getAllowedPurposes(string $modelClass): array
    {
        return LinkableRegistry::getAllowedPurposes($modelClass);
    }

    /**
     * Obtener propósito por defecto
     */
    public static function getDefaultPurpose(string $modelClass): string
    {
        return LinkableRegistry::getDefaultPurpose($modelClass);
    }

    /**
     * Obtener política de acceso por defecto
     */
    public static function getDefaultAccessPolicy(string $modelClass): string
    {
        return LinkableRegistry::getDefaultAccessPolicy($modelClass);
    }
}
