<?php

namespace App\Filament\Resources\Ecommerce\CategoryResource\Pages;

use Filament\Actions;
use LBCDev\Ecommerce\Models\Category;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\Ecommerce\CategoryResource;

class EditCategory extends EditRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {

        if ($data['parent_id'] === Category::root()->id) {
            $data['parent_id'] = null;
        }

        return $data;
    }

    protected function beforeFill(): void
    {
        abort_if($this->record->id === Category::root()->id, 403);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (!isset($data['parent_id']) || empty($data['parent_id'])) {
            $data['parent_id'] = Category::root()->id;
        }

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // $data['type'] = config('lbcdev-ecommerce.categories_taxonomy_type');

        $record->update($data);

        return $record;
    }
}
