<?php

namespace App\Http\Responses;

use Filament\Http\Responses\Auth\Contracts\LogoutResponse as Responsable;
use Illuminate\Http\RedirectResponse;

class LogoutResponse implements Responsable
{
    public function toResponse($request): RedirectResponse
    {
        // Detectar desde qué panel se está haciendo logout
        $currentUrl = $request->url();

        // Si es desde el panel admin
        if (str_contains($currentUrl, '/admin')) {
            return redirect()->route('home');
        }

        // Si es desde el panel app (cuando lo tengas)
        if (str_contains($currentUrl, '/app')) {
            return redirect()->route('home');
        }

        // Por defecto, redirigir al home
        return redirect()->route('home');
    }
}
