<?php

namespace LBCDev\Ecommerce\Services\CartStorage;

use LBCDev\Ecommerce\Contracts\CartStorageInterface;
use LBCDev\Ecommerce\Models\Cart;
use Illuminate\Support\Facades\Session;

class SessionCartStorage implements CartStorageInterface
{
    protected string $sessionKey;

    public function __construct()
    {
        $this->sessionKey = config('ecommerce.cart.session_key', 'ecommerce_cart');
    }

    public function getCart(string $identifier): Cart
    {
        $cartData = Session::get($this->getSessionKey($identifier));

        if (!$cartData) {
            return new Cart();
        }

        return $this->deserializeCart($cartData);
    }

    public function saveCart(string $identifier, Cart $cart): void
    {
        $cartData = $this->serializeCart($cart);
        Session::put($this->getSessionKey($identifier), $cartData);
    }

    public function clearCart(string $identifier): void
    {
        Session::forget($this->getSessionKey($identifier));
    }

    public function hasCart(string $identifier): bool
    {
        return Session::has($this->getSessionKey($identifier));
    }

    public function mergeCarts(string $fromIdentifier, string $toIdentifier): Cart
    {
        $fromCart = $this->getCart($fromIdentifier);
        $toCart = $this->getCart($toIdentifier);

        // Fusionar items
        foreach ($fromCart->getItems() as $item) {
            $toCart->addItem($item->getItem(), $item->getQuantity());
        }

        // Guardar resultado y limpiar origen
        $this->saveCart($toIdentifier, $toCart);
        $this->clearCart($fromIdentifier);

        return $toCart;
    }

    protected function getSessionKey(string $identifier): string
    {
        return $this->sessionKey . '_' . $identifier;
    }

    protected function serializeCart(Cart $cart): array
    {
        $items = [];

        foreach ($cart->getItems() as $item) {
            $items[] = [
                'sellable_type' => get_class($item->getItem()),
                'sellable_id' => $item->getId(),
                'quantity' => $item->getQuantity(),
            ];
        }

        return ['items' => $items];
    }

    protected function deserializeCart(array $cartData): Cart
    {
        $cart = new Cart();

        foreach ($cartData['items'] ?? [] as $itemData) {
            $sellableClass = $itemData['sellable_type'];

            if (class_exists($sellableClass)) {
                $sellable = $sellableClass::find($itemData['sellable_id']);

                if ($sellable) {
                    $cart->addItem($sellable, $itemData['quantity']);
                }
            }
        }

        return $cart;
    }
}
