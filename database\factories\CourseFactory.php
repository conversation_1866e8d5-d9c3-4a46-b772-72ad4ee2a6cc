<?php

// database\factories\CourseFactory.php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Course>
 */
class CourseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'titulo' => fake()->sentence(),
            'descripcion' => fake()->paragraph(),
            'slug' => fake()->slug(),
            'publicado' => fake()->boolean(),
        ];
    }

    public function withLessons(int $count = 3)
    {
        return $this->has(
            LessonFactory::new()
                ->count($count)
                ->sequence(
                    ...array_map(fn($i) => ['orden' => $i], range(1, $count))
                )
        );
    }
}
