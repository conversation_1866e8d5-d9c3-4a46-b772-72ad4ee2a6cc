<?php

namespace App\Filament\Resources\Ecommerce;


use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Log;
use LBCDev\Ecommerce\Models\Product;
use Filament\Forms\Components\Select;
use LBCDev\Ecommerce\Models\Category;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;

use App\Filament\Resources\Ecommerce\ProductResource\Pages\ListProducts;
use App\Filament\Resources\Ecommerce\ProductResource\Pages\CreateProduct;
use App\Filament\Resources\Ecommerce\ProductResource\Pages\EditProduct;
use App\Filament\Resources\Ecommerce\ProductResource\RelationManagers\FilesRelationManager;
use App\Filament\Resources\Ecommerce\ProductResource\RelationManagers\CoursesRelationManager;
use App\Filament\Resources\Ecommerce\ProductResource\RelationManagers\LinkablesRelationManager;
use Filament\Forms\Components\Placeholder;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'E-commerce';
    protected static null|int $navigationSort = 3;
    protected static bool $shouldRegisterNavigation = true;
    protected static ?string $modelLabel = 'Producto';
    protected static ?string $pluralModelLabel = 'Productos';
    protected static ?string $navigationLabel = 'Productos';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        Forms\Components\TextInput::make('name')->required(),
                        Forms\Components\Textarea::make('description'),
                        Forms\Components\Toggle::make('is_active')->required(),
                    ]),
                Section::make('Precios')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('price')
                            ->numeric()
                            ->minValue(0)
                            ->live()
                            ->columnSpanFull()
                            ->required(),
                        Forms\Components\TextInput::make('price_discount')
                            ->numeric()
                            ->live()
                            ->columns(1)
                            ->minValue(0)
                            ->maxValue(function ($state, $get) {
                                return $get('price');
                            }),
                        Placeholder::make('price_with_discount')
                            ->live()
                            ->content(function ($state, $get) {
                                $price = $get('price');
                                $discount = $get('price_discount');
                                return $price - $discount;
                            }),
                        Forms\Components\TextInput::make('tax_rate')
                            ->numeric()
                            ->live()
                            ->minValue(0)
                            ->maxValue(100)
                            ->required(),
                        Placeholder::make('price_with_tax')
                            ->live()
                            ->content(function ($state, $get) {
                                $price = $get('price');
                                $discount = $get('price_discount');

                                $price = $price - $discount;
                                $taxRate = $get('tax_rate');
                                return $price * (1 + $taxRate / 100);
                            }),
                    ]),
                Section::make('Categorías y Etiquetas')->schema([
                    Select::make('categories')
                        ->multiple()
                        ->options(function () {
                            return Category::all()
                                ->where('id', '!=', Category::root()->id)
                                ->pluck('name', 'id');
                        })
                        ->afterStateHydrated(function (Select $component, $state, $record) {
                            if ($record) {
                                $categoryIds = $record->categories()->pluck('id')->toArray();
                                $component->state($categoryIds);
                                Log::info('Categories hydrated (direct method):', $categoryIds);
                            }
                        })
                        ->preload()
                        ->searchable(),
                    Select::make('tags')
                        ->multiple()
                        ->options(function () {
                            $options = \LBCDev\Ecommerce\Models\Tag::all()->pluck('name', 'id');
                            Log::info('Tags options:', $options->toArray());
                            return $options;
                        })
                        ->afterStateHydrated(function (Select $component, $state, $record) {
                            if ($record) {
                                $tagIds = $record->tags()->pluck('id')->toArray();
                                $component->state($tagIds);
                                Log::info('Tags hydrated (via collection):', $tagIds);
                            }
                        })
                        ->preload()
                        ->searchable(),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->searchable(),
                TextColumn::make('price')->money(config('ecommerce.currency')),
                ToggleColumn::make('is_active'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            LinkablesRelationManager::class,
            FilesRelationManager::class,
            CoursesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProducts::route('/'),
            'create' => CreateProduct::route('/create'),
            'edit' => EditProduct::route('/{record}/edit'),
        ];
    }
}
