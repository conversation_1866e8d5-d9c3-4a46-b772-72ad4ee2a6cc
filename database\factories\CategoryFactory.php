<?php

namespace LBCDev\Ecommerce\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use LBCDev\Ecommerce\Models\Category;
use Illuminate\Support\Str;

class CategoryFactory extends Factory
{
    protected $model = Category::class;

    public function definition(): array
    {
        $name = $this->faker->unique()->words(2, true);

        return [
            'name'        => ucfirst($name),
            'slug'        => Str::slug($name),
            'description' => $this->faker->sentence(),
            'type'        => config('lbcdev-ecommerce.categories_taxonomy_type'),
            'parent_id'   => null, // O puedes asociarla más adelante
        ];
    }

    /**
     * Asocia una categoría padre específica.
     */
    public function withParent(Category $parent): static
    {
        return $this->state(fn() => ['parent_id' => $parent->id]);
    }
}
