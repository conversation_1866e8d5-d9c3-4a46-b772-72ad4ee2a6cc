<?php

namespace LBCDev\Ecommerce\Services\CartStorage;

use LBCDev\Ecommerce\Contracts\CartStorageInterface;
use LBCDev\Ecommerce\Models\Cart;
use LBCDev\Ecommerce\Models\PersistentCart;

class DatabaseCartStorage implements CartStorageInterface
{
    public function getCart(string $identifier): Cart
    {
        $persistentCart = $this->findPersistentCart($identifier);

        if (!$persistentCart) {
            return new Cart();
        }

        return $persistentCart->toMemoryCart();
    }

    public function saveCart(string $identifier, Cart $cart): void
    {
        $persistentCart = $this->findOrCreatePersistentCart($identifier);
        $persistentCart->updateFromMemoryCart($cart);
    }

    public function clearCart(string $identifier): void
    {
        $persistentCart = $this->findPersistentCart($identifier);

        if ($persistentCart) {
            $persistentCart->delete();
        }
    }

    public function hasCart(string $identifier): bool
    {
        return $this->findPersistentCart($identifier) !== null;
    }

    public function mergeCarts(string $fromIdentifier, string $toIdentifier): Cart
    {
        $fromCart = $this->getCart($fromIdentifier);
        $toCart = $this->getCart($toIdentifier);

        // Fusionar items
        foreach ($fromCart->getItems() as $item) {
            $toCart->addItem($item->getItem(), $item->getQuantity());
        }

        // Guardar resultado y limpiar origen
        $this->saveCart($toIdentifier, $toCart);
        $this->clearCart($fromIdentifier);

        return $toCart;
    }

    protected function findPersistentCart(string $identifier): ?PersistentCart
    {
        return PersistentCart::where('identifier', $identifier)
            ->active()
            ->first();
    }

    protected function findOrCreatePersistentCart(string $identifier): PersistentCart
    {
        return PersistentCart::firstOrCreate(
            ['identifier' => $identifier],
            [
                'expires_at' => now()->addDays(config('ecommerce.cart.expires_after_days', 30)),
                'total' => 0,
                'total_with_tax' => 0,
            ]
        );
    }
}
