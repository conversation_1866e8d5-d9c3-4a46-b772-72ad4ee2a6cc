#### API de HasTaxonomy

```php
<?php

// Métodos estáticos
public static function bootHasTaxonomy(): void

// Relaciones
public function taxonomies(string $name = 'taxonomable'): MorphToMany
public function taxonomiesOfType(string|TaxonomyType $type, string $name = 'taxonomable'): Collection

// Operaciones de taxonomías
public function attachTaxonomies($taxonomies, string $name = 'taxonomable'): self
public function detachTaxonomies($taxonomies = null, string $name = 'taxonomable'): self
public function syncTaxonomies($taxonomies, string $name = 'taxonomable'): self
public function toggleTaxonomies($taxonomies, string $name = 'taxonomable'): self

// Verificaciones
public function hasTaxonomies($taxonomies, string $name = 'taxonomable'): bool
public function hasAllTaxonomies($taxonomies, string $name = 'taxonomable'): bool
public function hasTaxonomyType(string|TaxonomyType $type, string $name = 'taxonomable'): bool
public function hasAncestorTaxonomy(int $taxonomyId): bool
public function hasDescendantTaxonomy(int $taxonomyId): bool

// Scopes de consulta
public function scopeWithAnyTaxonomies(Builder $query, $taxonomies, string $name = 'taxonomable'): Builder
public function scopeWithAllTaxonomies(Builder $query, $taxonomies, string $name = 'taxonomable'): Builder
public function scopeWithTaxonomyType(Builder $query, string|TaxonomyType $type, string $name = 'taxonomable'): Builder
public function scopeWithTaxonomy(Builder $query, $taxonomies, string $name = 'taxonomable'): Builder
public function scopeWithoutTaxonomies(Builder $query, $taxonomies, string $name = 'taxonomable'): Builder
public function scopeFilterByTaxonomies(Builder $query, array $filters, string $name = 'taxonomable'): Builder
public function scopeWithTaxonomySlug(Builder $query, string $slug, string|TaxonomyType|null $type = null, string $name = 'taxonomable'): Builder
public function scopeWithTaxonomyHierarchy(Builder $query, int $taxonomyId, bool $includeDescendants = true): Builder
public function scopeWithTaxonomyAtDepth(Builder $query, int $depth, string|TaxonomyType|null $type = null): Builder

// Métodos jerárquicos
public function getHierarchicalTaxonomies(string|TaxonomyType|null $type = null): Collection
public function getAncestorTaxonomies(string|TaxonomyType|null $type = null): Collection

// Método protegido utilitario
protected function getTaxonomyIds($taxonomies): array
```
