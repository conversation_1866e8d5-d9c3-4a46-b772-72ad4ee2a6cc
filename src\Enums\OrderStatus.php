<?php

namespace LBCDev\Ecommerce\Enums;


enum OrderStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case SHIPPED = 'shipped';
    case DELIVERED = 'delivered';
    case CANCELLED = 'cancelled';
    case REFUNDED = 'refunded';

    public static function labels(): array
    {
        return [
            self::PENDING->value => 'Pendiente',
            self::PROCESSING->value => 'Procesando',
            self::SHIPPED->value => 'Enviado',
            self::DELIVERED->value => 'Completado',
            self::CANCELLED->value => 'Cancelado',
            self::REFUNDED->value => 'Reembolsado',
        ];
    }

    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'gray',
            self::PROCESSING => 'blue',
            self::SHIPPED => 'yellow',
            self::DELIVERED => 'green',
            self::CANCELLED => 'red',
            self::REFUNDED => 'purple',
        };
    }

    public static function getPossibleValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
