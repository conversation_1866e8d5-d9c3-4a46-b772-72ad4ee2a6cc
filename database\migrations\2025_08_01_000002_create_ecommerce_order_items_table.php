<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('lbcdev_ecommerce_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('lbcdev_ecommerce_orders')->onDelete('cascade');

            // Relación polimórfica con el item vendible
            $table->string('sellable_type');
            $table->unsignedBigInteger('sellable_id');

            // Datos del item en el momento de la compra
            $table->integer('quantity');
            $table->decimal('unit_price', 10, 2);
            $table->decimal('tax_rate', 5, 2);
            $table->decimal('subtotal', 10, 2);
            $table->decimal('tax_amount', 10, 2);
            $table->decimal('total', 10, 2);

            $table->timestamps();

            // Índices
            $table->index(['sellable_type', 'sellable_id']);
            $table->index('order_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('lbcdev_ecommerce_order_items');
    }
};
