<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use LBCDev\Ecommerce\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $type = config('lbcdev-ecommerce.categories_taxonomy_type');

        // Creamos el nodo raíz de la taxonomía (actúa como contenedor de la categoría)
        $root = Category::firstOrCreate([
            'slug' => $type,
            'type' => $type,
        ], [
            'name' => 'Categorías',
            'description' => 'Categorías de productos',
        ]);

        // Términos hijos
        $terms = [
            ['name' => 'Audios', 'slug' => 'audios', 'description' => 'Audios'],
            ['name' => 'Cursos', 'slug' => 'cursos', 'description' => 'Cursos'],
            ['name' => 'Coaching', 'slug' => 'coaching', 'description' => 'Coaching'],
            ['name' => 'Varios', 'slug' => 'varios', 'description' => 'Varios'],
        ];

        foreach ($terms as $term) {
            Category::firstOrCreate([
                'slug' => $term['slug'],
                'type' => $type,
            ], [
                'name' => $term['name'],
                'description' => $term['description'],
                'parent_id' => $root->id,
            ]);
        }
    }
}
