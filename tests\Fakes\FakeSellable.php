<?php

namespace Tests\Fakes;

/**
 * Modelo falso que simula el Trait Sellable para test.
 */
class FakeSellable
{
    public $id = 123;
    public $title = 'Test Product';
    public $price = 100;
    public $tax_rate = 21;

    public function __construct($id = 123, $title = 'Test Product', $price = 100, $tax_rate = 21)
    {
        $this->id = $id;
        $this->title = $title;
        $this->price = $price;
        $this->tax_rate = $tax_rate;
    }

    public function getSellableName(): string
    {
        return $this->title;
    }

    public function getSellableId()
    {
        return $this->id;
    }

    public function getSellableType(): string
    {
        return 'FakeSellable';
    }

    public function getSellablePrice(): float
    {
        return $this->price;
    }

    public function getSellableTaxRate(): float
    {
        return $this->tax_rate;
    }

    public function getSellableTotalPrice(): float
    {
        return $this->price * (1 + $this->tax_rate / 100);
    }
}
