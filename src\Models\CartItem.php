<?php

namespace LBCDev\Ecommerce\Models;

use Illuminate\Database\Eloquent\Model;

class CartItem
{
    /**
     * El modelo vendible (que usa el Trait Sellable).
     *
     * @var mixed
     */
    protected $item;

    /**
     * Cantidad de unidades de este ítem en el carrito.
     *
     * @var int
     */
    protected int $quantity;

    /**
     * Constructor.
     *
     * @param mixed $item   Modelo vendible (debe implementar métodos Sellable)
     * @param int $quantity
     */
    public function __construct($item, int $quantity = 1)
    {
        $this->item = $item;
        $this->quantity = max(1, $quantity);
    }

    /**
     * Obtener el modelo vendible.
     *
     * @return mixed
     */
    public function getItem()
    {
        return $this->item;
    }

    /**
     * Obtener la cantidad.
     *
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * Modificar la cantidad.
     *
     * @param int $quantity
     * @return void
     */
    public function setQuantity(int $quantity): void
    {
        $this->quantity = max(1, $quantity);
    }

    /**
     * Obtener el precio unitario sin impuestos.
     *
     * @return float
     */
    public function getUnitPrice(): float
    {
        return $this->item->getSellablePrice();
    }

    /**
     * Obtener el precio unitario con impuestos.
     *
     * @return float
     */
    public function getUnitPriceWithTax(): float
    {
        return $this->item->getSellableTotalPrice();
    }

    /**
     * Obtener el subtotal sin impuestos (precio unitario * cantidad).
     *
     * @return float
     */
    public function getSubtotal(): float
    {
        return $this->getUnitPrice() * $this->quantity;
    }

    /**
     * Obtener el subtotal con impuestos (precio unitario con impuestos * cantidad).
     *
     * @return float
     */
    public function getSubtotalWithTax(): float
    {
        return $this->getUnitPriceWithTax() * $this->quantity;
    }

    /**
     * Obtener el nombre del ítem.
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->item->getSellableName();
    }

    /**
     * Obtener el identificador del ítem.
     *
     * @return mixed
     */
    public function getId()
    {
        return $this->item->getSellableId();
    }

    /**
     * Obtener el tipo/clase del ítem.
     *
     * @return string
     */
    public function getType(): string
    {
        return $this->item->getSellableType();
    }

    /**
     * Verificar si este ítem es igual a otro.
     *
     * @param mixed $sellable
     * @return bool
     */
    public function matches($sellable): bool
    {
        return $this->getId() === $sellable->getSellableId()
            && $this->getType() === get_class($sellable);
    }
}
