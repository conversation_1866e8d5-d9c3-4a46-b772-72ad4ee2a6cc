<?php

namespace App\Providers;

use App\Http\Responses\LogoutResponse;
use App\Http\Responses\RegisterResponse;
use App\Http\Responses\LoginResponse;
use Illuminate\Support\ServiceProvider;
use Filament\Http\Responses\Auth\Contracts\LogoutResponse as LogoutResponseContract;
use Filament\Http\Responses\Auth\Contracts\RegistrationResponse as RegistrationResponseContract;
use Filament\Http\Responses\Auth\Contracts\LoginResponse as LoginResponseContract;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Vincular la respuesta personalizada de logout
        $this->app->bind(LogoutResponseContract::class, LogoutResponse::class);

        // Vincular la respuesta personalizada de registro
        $this->app->singleton(RegistrationResponseContract::class, RegisterResponse::class);

        // Vincular la respuesta personalizada de login (opcional)
        $this->app->singleton(LoginResponseContract::class, LoginResponse::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
