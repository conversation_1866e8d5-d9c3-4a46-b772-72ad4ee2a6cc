<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Activación del módulo e-commerce
    |--------------------------------------------------------------------------
    |
    | Puedes desactivar completamente el paquete con esta opción.
    |
    */

    'enabled' => true,

    /*
    |--------------------------------------------------------------------------
    | Moneda por defecto
    |--------------------------------------------------------------------------
    |
    | Usada en precios, formatos, pagos, etc.
    |
    */

    'currency' => env('ECOMMERCE_CURRENCY', 'EUR'),

    /*
    |--------------------------------------------------------------------------
    | Impuestos por defecto
    |--------------------------------------------------------------------------
    |
    | Porcentaje global de IVA o impuesto aplicado si no se define otro.
    |
    */

    'tax_rate' => env('ECOMMERCE_TAX_RATE', 21.0),

    /*
    |--------------------------------------------------------------------------
    | Order Configuration
    |--------------------------------------------------------------------------
    */
    'order_number_prefix' => env('ECOMMERCE_ORDER_PREFIX', 'ORD'),

    /*
    |--------------------------------------------------------------------------
    | Cart Configuration
    |--------------------------------------------------------------------------
    */
    'cart' => [
        // Driver: session, database, hybrid
        'driver' => env('ECOMMERCE_CART_DRIVER', 'hybrid'),

        // Session configuration
        'session_key' => 'ecommerce_cart',

        // Database configuration
        'expires_after_days' => env('ECOMMERCE_CART_EXPIRES_DAYS', 30),

        // Auto-cleanup
        'auto_cleanup' => env('ECOMMERCE_CART_AUTO_CLEANUP', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Configuration
    |--------------------------------------------------------------------------
    */
    'send_order_confirmation' => env('ECOMMERCE_SEND_ORDER_CONFIRMATION', true),
    'admin_email' => env('ECOMMERCE_ADMIN_EMAIL'),


    /*
    |--------------------------------------------------------------------------
    | Taxonomy Configuration
    |--------------------------------------------------------------------------
    */
    'categories_taxonomy_type' => 'lbcdev-ecommerce-category',
    'tags_taxonomy_type' => 'lbcdev-ecommerce-tag',
    'categories_table' => 'taxonomies',
    'taxonomy_slug_prefixes' => [
        'category' => 'cat--',
        'tag' => 'tag--',
    ],

    /*
    |--------------------------------------------------------------------------
    | Modelos personalizados
    |--------------------------------------------------------------------------
    |
    | Permite al consumidor del paquete usar sus propios modelos si lo desea.
    |
    */

    'models' => [
        // 'product' => \LBCDev\Ecommerce\Models\Product::class,
        // 'order' => \App\Models\CustomOrder::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Linkable Types Registry
    |--------------------------------------------------------------------------
    |
    | Registro de tipos de modelos que pueden ser vinculados a productos.
    | La aplicación puede registrar sus propios tipos usando el método
    | Ecommerce::registerLinkable() en un service provider.
    |
    */

    'linkables' => [
        // Ejemplo:
        // \App\Models\File::class => [
        //     'label' => 'Archivo',
        //     'icon' => 'heroicon-o-document',
        //     'allowed_purposes' => ['primary', 'included', 'bonus'],
        //     'default_purpose' => 'included',
        //     'default_access_policy' => 'after_payment',
        // ],
    ],

];
