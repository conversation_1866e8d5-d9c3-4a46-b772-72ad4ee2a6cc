<?php

namespace LBCDev\Ecommerce\Models;

use LBCDev\Ecommerce\Traits\Sellable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use LBCDev\Ecommerce\Database\Factories\ProductVariantFactory;

class ProductVariant extends Model
{
    use Sellable;
    use HasFactory;

    protected $table = 'lbcdev_ecommerce_product_variants';

    protected $fillable = [
        'product_id',
        'sku',
        'price',
        'tax_rate',
        'attributes', // ['size' => 'M', 'color' => 'red']
        'is_active',
    ];

    protected $casts = [
        'price' => 'float',
        'tax_rate' => 'float',
        'attributes' => 'array',
        'is_active' => 'boolean',
    ];

    public static function booted(): void
    {
        static::saving(function ($variant) {
            if (empty($variant->sku)) {
                throw new \InvalidArgumentException('SKU es obligatorio para cada variante.');
            }

            $exists = static::where('sku', $variant->sku)
                ->where('id', '!=', $variant->id)
                ->exists();

            if ($exists) {
                throw new \InvalidArgumentException('SKU duplicado: ' . $variant->sku);
            }
        });
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function getSellableName(): string
    {
        $baseName = $this->product?->name ?? 'Unnamed';
        $attrStr = collect($this->attributes)
            ->map(fn($val, $key) => ucfirst($key) . ': ' . $val)
            ->join(', ');

        return "{$baseName} ({$attrStr})";
    }

    public function getSellablePrice(): float
    {
        return $this->price > 0
            ? $this->price
            : ($this->product?->price ?? 0);
    }

    public function getSellableTaxRate(): float
    {
        return $this->tax_rate > 0
            ? $this->tax_rate
            : ($this->product?->tax_rate ?? 0);
    }

    protected static function newFactory()
    {
        return ProductVariantFactory::new();
    }
}
