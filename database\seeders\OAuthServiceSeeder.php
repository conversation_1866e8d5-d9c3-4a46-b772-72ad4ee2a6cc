<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use LBCDev\OAuthManager\Models\OAuthService;

class OAuthServiceSeeder extends Seeder
{
    public function run(): void
    {
        $services = [
            [
                'name' => 'Google Drive Principal',
                'service_type' => 'google_drive',
                'slug' => 'google-drive-principal',
                'credentials' => [
                    'client_id' => env('GOOGLE_DRIVE_CLIENT_ID', ''),
                    'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET', ''),
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Google Drive Secundario',
                'service_type' => 'google_drive',
                'slug' => 'google-drive-secundario',
                'credentials' => [
                    'client_id' => env('GOOGLE_DRIVE_CLIENT_ID', ''),
                    'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET', ''),
                ],
                'is_active' => false,
            ],
        ];

        foreach ($services as $service) {
            OAuthService::updateOrCreate(
                [
                    'name' => $service['name'],
                    'service_type' => $service['service_type']
                ],
                $service
            );
        }
    }
}
