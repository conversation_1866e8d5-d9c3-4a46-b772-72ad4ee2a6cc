<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::create('user_course_access', function (Blueprint $table) {
        //     $table->id();
        //     $table->foreignId('user_id')->constrained()->cascadeOnDelete();
        //     $table->foreignId('course_id')->constrained()->cascadeOnDelete();
        //     $table->foreignId('order_id')->nullable()->constrained()->nullOnDelete(); // opcional
        //     $table->timestamp('fecha_acceso')->useCurrent();
        //     $table->timestamp('expira_en')->nullable(); // si hay tiempo limitado

        //     $table->unique(['user_id', 'course_id']); // evita duplicados
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::dropIfExists('user_course_accesses');
    }
};
