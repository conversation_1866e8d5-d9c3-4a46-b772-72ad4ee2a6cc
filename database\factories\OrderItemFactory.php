<?php

namespace LBCDev\Ecommerce\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use LBCDev\Ecommerce\Models\Product;

class OrderItemFactory extends Factory
{
    protected $model = \LBCDev\Ecommerce\Models\OrderItem::class;

    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 10);

        $product = Product::all()->random();
        if (!$product) {
            $product = ProductFactory::new()->create();
        }
        $unitPrice = $product->price;
        $taxRate = $product->tax_rate;
        $subtotal = $unitPrice * $quantity;
        $taxAmount = $subtotal * ($taxRate / 100);
        $total = $subtotal + $taxAmount;

        return [
            'order_id' => OrderFactory::new()->create()->id,
            'sellable_type' => Product::class,
            'sellable_id' => $product->id,
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'tax_rate' => $taxRate,
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total' => $total,
        ];
    }
}
