<?php

namespace App\Filament\Resources\Ecommerce\ProductResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\Course;
use LBCDev\Ecommerce\Facades\Ecommerce;

class CoursesRelationManager extends RelationManager
{
    protected static string $relationship = 'linkables';

    protected static ?string $title = 'Cursos Vinculados';

    protected static ?string $modelLabel = 'curso';

    protected static ?string $pluralModelLabel = 'cursos';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('productable_id')
                    ->label('Curso')
                    ->options(Course::all()->pluck('titulo', 'id'))
                    ->required()
                    ->searchable(),

                Forms\Components\Select::make('purpose')
                    ->label('Propósito')
                    ->options([
                        'primary' => 'Principal',
                        'grants_access' => 'Otorga Acceso',
                    ])
                    ->default('grants_access')
                    ->required(),

                Forms\Components\Select::make('access_policy')
                    ->label('Política de Acceso')
                    ->options([
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                    ])
                    ->default('after_payment')
                    ->required(),

                Forms\Components\KeyValue::make('meta')
                    ->label('Metadatos')
                    ->keyLabel('Clave')
                    ->valueLabel('Valor')
                    ->addActionLabel('Añadir metadato')
                    ->helperText('Configuración adicional como duración del acceso, certificados, etc.'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('titulo')
            ->modifyQueryUsing(fn(Builder $query) => $query->where('productable_type', Course::class))
            ->columns([
                Tables\Columns\TextColumn::make('productable.titulo')
                    ->label('Título del Curso')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('productable.descripcion')
                    ->label('Descripción')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\IconColumn::make('productable.publicado')
                    ->label('Publicado')
                    ->boolean(),

                Tables\Columns\TextColumn::make('purpose')
                    ->label('Propósito')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'primary' => 'primary',
                        'grants_access' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'primary' => 'Principal',
                        'grants_access' => 'Otorga Acceso',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('access_policy')
                    ->label('Política de Acceso')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'immediate' => 'success',
                        'after_payment' => 'warning',
                        'scheduled' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Vinculado el')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('purpose')
                    ->label('Propósito')
                    ->options([
                        'primary' => 'Principal',
                        'grants_access' => 'Otorga Acceso',
                    ]),

                Tables\Filters\SelectFilter::make('access_policy')
                    ->label('Política de Acceso')
                    ->options([
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                    ]),

                Tables\Filters\TernaryFilter::make('productable.publicado')
                    ->label('Curso Publicado')
                    ->placeholder('Todos los cursos')
                    ->trueLabel('Solo cursos publicados')
                    ->falseLabel('Solo cursos no publicados'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['productable_type'] = Course::class;
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
