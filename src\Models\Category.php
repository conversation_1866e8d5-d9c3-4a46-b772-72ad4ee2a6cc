<?php

namespace LBCDev\Ecommerce\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use LBCDev\Ecommerce\Helpers\TaxonomySlugHelper;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use LBCDev\Ecommerce\Database\Factories\CategoryFactory;

class Category extends Taxonomy
{
    use HasFactory;

    protected $table = 'taxonomies';

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        // Asegurar que el atributo 'type' esté definido antes de llamar al constructor padre
        $type = config('lbcdev-ecommerce.categories_taxonomy_type', 'lbcdev-ecommerce-category');

        if (!array_key_exists('type', $attributes) || empty($attributes['type'])) {
            $attributes['type'] = $type;
        }

        // // Asignar slug con prefijo
        // if (!empty($attributes['slug'])) {
        //     $attributes['slug'] = TaxonomySlugHelper::withPrefix($attributes['slug'], $type);
        // } elseif (!empty($attributes['name'])) {
        //     $slug = Str::slug($attributes['name']);
        //     $attributes['slug'] = TaxonomySlugHelper::withPrefix($slug, $type);
        // }

        parent::__construct($attributes);

        $this->table = config('lbcdev-ecommerce.categories_table', 'taxonomies');
    }

    protected static function booted(): void
    {
        static::addGlobalScope('ecommerce_category', function (Builder $query) {
            $query->where('type', config('lbcdev-ecommerce.categories_taxonomy_type', 'lbcdev-ecommerce-category'));
        });
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public static function root(): self
    {
        return static::where('slug', config('lbcdev-ecommerce.categories_taxonomy_type'))->firstOrFail();
    }

    protected static function newFactory()
    {
        return CategoryFactory::new();
    }
}
