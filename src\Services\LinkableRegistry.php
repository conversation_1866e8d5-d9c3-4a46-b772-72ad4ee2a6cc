<?php

namespace LBCDev\Ecommerce\Services;

use Illuminate\Support\Collection;

class LinkableRegistry
{
    protected static array $linkables = [];

    /**
     * Registrar un tipo linkable
     */
    public static function register(string $modelClass, array $config = []): void
    {
        $defaultConfig = [
            'label' => class_basename($modelClass),
            'icon' => 'heroicon-o-cube',
            'allowed_purposes' => ['primary', 'included', 'bonus', 'grants_access'],
            'default_purpose' => 'included',
            'default_access_policy' => 'after_payment',
        ];

        static::$linkables[$modelClass] = array_merge($defaultConfig, $config);
    }

    /**
     * Obtener todos los tipos linkables registrados
     */
    public static function all(): array
    {
        return static::$linkables;
    }

    /**
     * Obtener configuración de un tipo específico
     */
    public static function get(string $modelClass): ?array
    {
        return static::$linkables[$modelClass] ?? null;
    }

    /**
     * Verificar si un tipo está registrado
     */
    public static function isRegistered(string $modelClass): bool
    {
        return isset(static::$linkables[$modelClass]);
    }

    /**
     * Obtener tipos como Collection para uso en formularios
     */
    public static function getForSelect(): Collection
    {
        return collect(static::$linkables)->map(function ($config, $class) {
            return [
                'value' => $class,
                'label' => $config['label'],
                'icon' => $config['icon'],
            ];
        })->values();
    }

    /**
     * Obtener propósitos permitidos para un tipo
     */
    public static function getAllowedPurposes(string $modelClass): array
    {
        $config = static::get($modelClass);
        return $config['allowed_purposes'] ?? ['primary', 'included', 'bonus', 'grants_access'];
    }

    /**
     * Obtener propósito por defecto para un tipo
     */
    public static function getDefaultPurpose(string $modelClass): string
    {
        $config = static::get($modelClass);
        return $config['default_purpose'] ?? 'included';
    }

    /**
     * Obtener política de acceso por defecto para un tipo
     */
    public static function getDefaultAccessPolicy(string $modelClass): string
    {
        $config = static::get($modelClass);
        return $config['default_access_policy'] ?? 'after_payment';
    }

    /**
     * Limpiar el registry (útil para testing)
     */
    public static function clear(): void
    {
        static::$linkables = [];
    }

    /**
     * Cargar tipos desde configuración
     */
    public static function loadFromConfig(): void
    {
        $configLinkables = config('lbcdev-ecommerce.linkables', []);
        
        foreach ($configLinkables as $modelClass => $config) {
            static::register($modelClass, $config);
        }
    }
}
