<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Database\Seeders\CmsSeeder;
use Illuminate\Database\Seeder;
use Database\Seeders\CourseSeeder;
use Database\Seeders\CategorySeeder;
use Database\Seeders\OAuthServiceSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RoleYPermissionSeeder::class,
            UserSeeder::class,
            HomePageSectionSeeder::class,
            LanguageSeeder::class,
            FeatureSeeder::class,
            TestimonialSeeder::class,
            CategorySeeder::class,
            TagSeeder::class,
            ProductSeeder::class,
            OrderSeeder::class,
            // CourseSeeder::class,
            OAuthServiceSeeder::class,
            CmsSeeder::class,
        ]);
    }
}
