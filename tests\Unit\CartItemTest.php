<?php

namespace Tests\Unit;

use Tests\Fakes\FakeSellable;
use PHPUnit\Framework\TestCase;
use LBCDev\Ecommerce\Models\CartItem;

class CartItemTest extends TestCase
{
    public function testConstructorAndGetters()
    {
        $item = new FakeSellable();
        $cartItem = new CartItem($item, 3);

        $this->assertSame($item, $cartItem->getItem());
        $this->assertEquals(3, $cartItem->getQuantity());
        $this->assertEquals('Test Product', $cartItem->getName());
        $this->assertEquals(123, $cartItem->getId());
        $this->assertEquals('FakeSellable', $cartItem->getType());
    }

    public function testQuantityCannotBeLessThanOne()
    {
        $item = new FakeSellable();

        $cartItem = new CartItem($item, 0);
        $this->assertEquals(1, $cartItem->getQuantity());

        $cartItem->setQuantity(-5);
        $this->assertEquals(1, $cartItem->getQuantity());

        $cartItem->setQuantity(10);
        $this->assertEquals(10, $cartItem->getQuantity());
    }

    public function testPriceCalculations()
    {
        $item = new FakeSellable();
        $cartItem = new CartItem($item, 2);

        $this->assertEquals(100, $cartItem->getUnitPrice());
        $this->assertEquals(121, $cartItem->getUnitPriceWithTax());
        $this->assertEquals(200, $cartItem->getSubtotal());
        $this->assertEquals(242, $cartItem->getSubtotalWithTax());
    }
}
