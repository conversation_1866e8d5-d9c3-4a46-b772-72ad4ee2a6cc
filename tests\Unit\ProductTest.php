<?php

namespace LBCDev\Ecommerce\Tests\Unit;

use Orchestra\Testbench\TestCase;
use LBCDev\Ecommerce\Models\Product;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use LBCDev\Ecommerce\Models\ProductLinkable;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Fakes\FakeFile;
use Tests\Fakes\FakeCourse;
use Tests\Fakes\FakeTaxonomy;
use LBCDev\Ecommerce\Models\ProductVariant;

class ProductTest extends TestCase
{
    use RefreshDatabase;

    protected function getPackageProviders($app)
    {
        return [
            \LBCDev\Ecommerce\EcommerceServiceProvider::class,
        ];
    }

    protected function getEnvironmentSetUp($app)
    {
        // Configurar SQLite en memoria
        $app['config']->set('database.default', 'sqlite');
        $app['config']->set('database.connections.sqlite', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);

        // Configurar taxonomías
        $app['config']->set('lbcdev-ecommerce.categories_taxonomy_type', 'lbcdev-ecommerce-category');
        $app['config']->set('lbcdev-ecommerce.tags_taxonomy_type', 'lbcdev-ecommerce-tag');
        $app['config']->set('lbcdev-ecommerce.categories_table', 'taxonomies');
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Crear tabla taxonomables manualmente para los tests
        if (!Schema::hasTable('taxonomables')) {
            Schema::create('taxonomables', function (Blueprint $table) {
                $table->id();
                $table->string('taxonomable_type');
                $table->unsignedBigInteger('taxonomable_id');
                $table->unsignedBigInteger('taxonomy_id');
                $table->timestamps();

                $table->index(['taxonomable_type', 'taxonomable_id']);
                $table->foreign('taxonomy_id')->references('id')->on('taxonomies')->onDelete('cascade');
            });
        }

        // Añadir columna deleted_at a taxonomies si no existe
        if (Schema::hasTable('taxonomies') && !Schema::hasColumn('taxonomies', 'deleted_at')) {
            Schema::table('taxonomies', function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    public function test_el_sistema_puede_crear_un_producto()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'description' => 'Descripción del producto',
            'price' => 99.99,
            'tax_rate' => 21,
            'is_active' => true,
        ]);

        // Verificar que el producto se guardó en la base de datos
        $this->assertDatabaseHas('lbcdev_ecommerce_products', [
            'name' => 'Producto de prueba',
            'price' => 99.99,
        ]);

        // Verificar atributos del modelo
        $this->assertEquals('Producto de prueba', $producto->name);
        $this->assertEquals(99.99, $producto->price);
        $this->assertTrue($producto->is_active);
    }

    public function test_el_precio_con_descuento_se_calcula_correctamente()
    {
        $producto = Product::create([
            'name' => 'Producto con descuento',
            'price' => 100,
            'price_discount' => 20,
            'tax_rate' => 10,
            'is_active' => true,
        ]);
        $this->assertEquals(80, $producto->getPriceWithDiscountAttribute());
    }

    // Test para verificar que la creación falla si falta el nombre
    public function test_la_creacion_falla_si_falta_el_nombre()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);
        Product::create([
            'description' => 'Descripción del producto',
            'price' => 99.99,
            'tax_rate' => 21,
        ]);
    }

    // Test para verificar que el stock tiene el valor por defecto de 0
    public function test_stock_es_cero_por_defecto()
    {
        $producto = Product::create([
            'name' => 'Producto sin stock',
            'price' => 10,
        ]);
        $this->assertEquals(0, $producto->stock);
    }

    // Test para verificar que un producto inactivo no es devuelto en una consulta de productos activos
    public function test_la_consulta_solo_devuelve_productos_activos()
    {
        Product::create(['name' => 'Producto activo', 'price' => 10, 'is_active' => true]);
        Product::create(['name' => 'Producto inactivo', 'price' => 10, 'is_active' => false]);

        $productosActivos = Product::where('is_active', true)->get();

        $this->assertCount(1, $productosActivos);
        $this->assertEquals('Producto activo', $productosActivos->first()->name);
    }

    // Test para verificar que se pueden asociar y obtener linkables
    public function test_se_pueden_asociar_y_obtener_linkables()
    {
        $producto = Product::create([
            'name' => 'Producto con linkables',
            'price' => 50,
            'tax_rate' => 5,
            'is_active' => true,
        ]);

        // Crear un linkable ficticio
        $modeloFicticio = new class {
            public function getKey()
            {
                return 1;
            }
        };

        $linkable = $producto->linkModel($modeloFicticio, [
            'purpose' => 'test',
            'access_policy' => 'after_payment',
        ]);

        $this->assertInstanceOf(ProductLinkable::class, $linkable);
        $this->assertEquals('test', $linkable->purpose);
        $this->assertEquals('after_payment', $linkable->access_policy);
    }


    public function test_linkables_of_type_devuelve_solo_linkables_del_tipo_especificado()
    {
        $producto = Product::create([
            'name' => 'Producto con múltiples linkables',
            'price' => 75,
            'tax_rate' => 15,
            'is_active' => true,
        ]);

        // Crear modelos ficticios de diferentes tipos
        $archivo1 = new FakeFile(1, 'archivo1.pdf');
        $archivo2 = new FakeFile(2, 'archivo2.pdf');
        $curso = new FakeCourse(1, 'Curso de prueba');

        // Vincular diferentes tipos de modelos
        $producto->linkModel($archivo1, ['purpose' => 'primary']);
        $producto->linkModel($curso, ['purpose' => 'included']);
        $producto->linkModel($archivo2, ['purpose' => 'bonus']);

        // Obtener linkables de un tipo específico
        $linkablesDeArchivo = $producto->linkablesOfType(FakeFile::class)->get();
        $linkablesDeCurso = $producto->linkablesOfType(FakeCourse::class)->get();

        // Verificar que solo devuelve los del tipo correcto
        $this->assertCount(2, $linkablesDeArchivo);
        $this->assertCount(1, $linkablesDeCurso);

        // Verificar que los tipos son correctos
        foreach ($linkablesDeArchivo as $linkable) {
            $this->assertEquals(FakeFile::class, $linkable->productable_type);
        }

        foreach ($linkablesDeCurso as $linkable) {
            $this->assertEquals(FakeCourse::class, $linkable->productable_type);
        }
    }

    public function test_linkables_of_type_devuelve_query_builder_vacio_si_no_hay_coincidencias()
    {
        $producto = Product::create([
            'name' => 'Producto sin linkables específicos',
            'price' => 50,
            'is_active' => true,
        ]);

        // Crear un modelo ficticio
        $archivo = new FakeFile(1, 'archivo.pdf');

        // Vincular un modelo
        $producto->linkModel($archivo);

        // Buscar un tipo que no existe
        $linkablesInexistentes = $producto->linkablesOfType('App\\Models\\TipoInexistente')->get();

        $this->assertCount(0, $linkablesInexistentes);
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $linkablesInexistentes);
    }

    public function test_linkables_of_type_permite_encadenar_metodos_de_query()
    {
        $producto = Product::create([
            'name' => 'Producto para test de query',
            'price' => 100,
            'is_active' => true,
        ]);

        // Crear modelos ficticios
        $archivo1 = new FakeFile(1, 'archivo1.pdf');
        $archivo2 = new FakeFile(2, 'archivo2.pdf');

        // Vincular con diferentes propósitos
        $producto->linkModel($archivo1, ['purpose' => 'primary', 'access_policy' => 'immediate']);
        $producto->linkModel($archivo2, ['purpose' => 'bonus', 'access_policy' => 'after_payment']);

        // Usar linkablesOfType con filtros adicionales
        $linkablesPrimarios = $producto->linkablesOfType(FakeFile::class)
            ->where('purpose', 'primary')
            ->get();

        $linkablesConAccesoInmediato = $producto->linkablesOfType(FakeFile::class)
            ->where('access_policy', 'immediate')
            ->get();

        $this->assertCount(1, $linkablesPrimarios);
        $this->assertCount(1, $linkablesConAccesoInmediato);
        $this->assertEquals('primary', $linkablesPrimarios->first()->purpose);
        $this->assertEquals('immediate', $linkablesConAccesoInmediato->first()->access_policy);
    }

    public function test_linkables_of_type_funciona_con_relacion_eager_loading()
    {
        $producto = Product::create([
            'name' => 'Producto para eager loading',
            'price' => 80,
            'is_active' => true,
        ]);

        // Crear modelo ficticio
        $curso = new FakeCourse(1, 'Curso de prueba');

        // Vincular modelo
        $producto->linkModel($curso, ['purpose' => 'included']);

        // Verificar que se puede usar with() en la query
        $linkablesConProducto = $producto->linkablesOfType(FakeCourse::class)
            ->with('product')
            ->get();

        $this->assertCount(1, $linkablesConProducto);
        $this->assertTrue($linkablesConProducto->first()->relationLoaded('product'));
        $this->assertEquals($producto->id, $linkablesConProducto->first()->product->id);
    }

    public function test_variants_devuelve_relacion_hasMany_correcta()
    {
        $producto = Product::create([
            'name' => 'Producto con variantes',
            'price' => 100,
            'is_active' => true,
        ]);

        // Verificar que la relación existe y es del tipo correcto
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $producto->variants());

        // Verificar que inicialmente no hay variantes
        $this->assertCount(0, $producto->variants);
    }

    public function test_variants_puede_crear_y_obtener_variantes()
    {
        $producto = Product::create([
            'name' => 'Producto con variantes',
            'price' => 100,
            'is_active' => true,
        ]);

        // Crear variantes usando la relación
        $variante1 = $producto->variants()->create([
            'sku' => 'TEST-SKU-001',
            'price' => 120,
            'tax_rate' => 21,
            'attributes' => ['size' => 'M', 'color' => 'red'],
            'is_active' => true,
        ]);

        $variante2 = $producto->variants()->create([
            'sku' => 'TEST-SKU-002',
            'price' => 130,
            'tax_rate' => 21,
            'attributes' => ['size' => 'L', 'color' => 'blue'],
            'is_active' => true,
        ]);

        // Verificar que las variantes se crearon correctamente
        $this->assertCount(2, $producto->variants);
        $this->assertEquals('TEST-SKU-001', $producto->variants->first()->sku);
        $this->assertEquals('TEST-SKU-002', $producto->variants->last()->sku);

        // Verificar que las variantes pertenecen al producto correcto
        $this->assertEquals($producto->id, $variante1->product_id);
        $this->assertEquals($producto->id, $variante2->product_id);
    }

    public function test_variants_mantienen_referencia_al_producto_correcto()
    {
        $producto = Product::create([
            'name' => 'Producto con variantes',
            'price' => 100,
            'is_active' => true,
        ]);

        // Crear una variante
        $variante = $producto->variants()->create([
            'sku' => 'TEST-SKU-REF',
            'price' => 120,
            'tax_rate' => 21,
            'is_active' => true,
        ]);

        // Verificar que la variante tiene la referencia correcta al producto
        $this->assertEquals($producto->id, $variante->product_id);

        // Verificar que se puede acceder al producto desde la variante
        $this->assertInstanceOf(Product::class, $variante->product);
        $this->assertEquals($producto->name, $variante->product->name);

        // Verificar que la variante está en la colección de variantes del producto
        $this->assertTrue($producto->variants->contains($variante));
    }

    public function test_get_categories_attribute_existe_y_es_callable()
    {
        $producto = Product::create([
            'name' => 'Producto con categorías',
            'price' => 100,
            'is_active' => true,
        ]);

        // Verificar que el método existe y es callable
        $this->assertTrue(method_exists($producto, 'getCategoriesAttribute'));
        $this->assertTrue(is_callable([$producto, 'getCategoriesAttribute']));

        // Verificar que el método no lanza excepciones al ejecutarse
        $result = $producto->getCategoriesAttribute();
        $this->assertNotNull($result);
    }

    public function test_get_tags_attribute_existe_y_es_callable()
    {
        $producto = Product::create([
            'name' => 'Producto con tags',
            'price' => 100,
            'is_active' => true,
        ]);

        // Verificar que el método existe y es callable
        $this->assertTrue(method_exists($producto, 'getTagsAttribute'));
        $this->assertTrue(is_callable([$producto, 'getTagsAttribute']));

        // Verificar que el método no lanza excepciones al ejecutarse
        $result = $producto->getTagsAttribute();
        $this->assertNotNull($result);
    }

    public function test_traits_has_categories_y_has_tags_estan_aplicados()
    {
        $producto = new Product();

        // Verificar que los traits están aplicados verificando métodos específicos
        $this->assertTrue(method_exists($producto, 'categoriesRelation'));
        $this->assertTrue(method_exists($producto, 'tagsRelation'));
        $this->assertTrue(method_exists($producto, 'addCategory'));
        $this->assertTrue(method_exists($producto, 'addTag'));
        $this->assertTrue(method_exists($producto, 'removeCategory'));
        $this->assertTrue(method_exists($producto, 'removeTag'));
    }

    public function test_el_metodo_new_factory_funciona_correctamente()
    {
        // Usar la factory para crear un producto en la base de datos
        $producto = Product::factory()->create([
            'name' => 'Producto con factory',
            'price' => 123.45,
            'tax_rate' => 10,
        ]);

        // Verificar que es instancia de Product
        $this->assertInstanceOf(Product::class, $producto);

        // Verificar que se guardó en la base de datos
        $this->assertDatabaseHas('lbcdev_ecommerce_products', [
            'name' => 'Producto con factory',
            'price' => 123.45,
        ]);

        // Verificar atributos cargados
        $this->assertEquals('Producto con factory', $producto->name);
        $this->assertEquals(123.45, $producto->price);
    }
}
