<?php

namespace LBCDev\Ecommerce\Models;

use Illuminate\Database\Eloquent\Builder;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use LBCDev\Ecommerce\Database\Factories\TagFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Tag extends Taxonomy
{
    protected $table = 'taxonomies';

    public function __construct(array $attributes = [])
    {
        // Asegurar que el atributo 'type' esté definido antes de llamar al constructor padre
        $attributes['type'] = config('lbcdev-ecommerce.tags_taxonomy_type', 'lbcdev-ecommerce-tag');

        parent::__construct($attributes);

        $this->table = config('lbcdev-ecommerce.categories_table', 'taxonomies');
    }

    protected static function booted(): void
    {
        static::addGlobalScope('ecommerce_tag', function (Builder $query) {
            $query->where('type', config('lbcdev-ecommerce.tags_taxonomy_type', 'lbcdev-ecommerce-tag'));
        });
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    // public function products(): MorphToMany
    // {
    //     return $this->morphedByMany(Product::class, 'taxonomable');
    // }

    protected static function newFactory()
    {
        return TagFactory::new();
    }
}
