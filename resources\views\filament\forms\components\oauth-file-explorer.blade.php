@php
    $oauthServiceField = $getOauthServiceField();
    $oauthServiceId = null;
    $serviceType = null;

    if ($oauthServiceField) {
        $oauthServiceId = data_get($getRecord(), $oauthServiceField) ?? $this->form->getRawState()[$oauthServiceField] ?? null;

        if ($oauthServiceId) {
            $oauthService = \LBCDev\OAuthManager\Models\OAuthService::find($oauthServiceId);
            $serviceType = $oauthService?->service_type;
        }
    }

    $serviceDisplayName = match ($serviceType) {
        'google_drive' => 'Google Drive',
        'dropbox' => 'Dropbox',
        'onedrive' => 'OneDrive',
        'youtube' => 'YouTube',
        'mega' => 'Mega',
        default => ucfirst(str_replace('_', ' ', $serviceType ?? 'Cloud Storage'))
    };

    $statePath = $getStatePath();
    $currentValue = $getState() ?? '';

    $currentUrl = is_array($currentValue) ? $currentValue['url'] ?? '' : $currentValue;
    $currentId = is_array($currentValue) ? $currentValue['id'] ?? null : null;
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div class="space-y-4"
         x-data="{
             init() {
                 const eventName = 'fileSelected-{{ str_replace('.', '-', $statePath) }}';
                 window.addEventListener(eventName, (event) => {
                     const { fileUrl, fileId } = event.detail;
                     this.updateFieldValue({ url: fileUrl, id: fileId });
                 });
             },
             updateFieldValue(value) {
                 try {
                     @this.set('{{ $statePath }}', value);
                     if (this.$refs.fileUrlInput) {
                         this.$refs.fileUrlInput.value = value.url;
                         this.$refs.fileUrlInput.dispatchEvent(new Event('input'));
                     }
                 } catch (error) {
                     console.error('Error updating field:', error);
                 }
             }
         }">

        {{-- Input --}}
        <input
            type="text"
            x-ref="fileUrlInput"
            value="{{ $currentUrl }}"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="File URL will appear here when selected..."
            readonly
        />

        {{-- Current file --}}
        @if($currentUrl)
            <div class="p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 13.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Selected file:</span>
                    </div>
                    <button
                        type="button"
                        wire:click="$set('{{ $statePath }}', '')"
                        class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        title="Clear selection"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="mt-2">
                    <a 
                        href="{{ $currentUrl }}" 
                        target="_blank" 
                        class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline break-all"
                    >
                        {{ $currentUrl }}
                    </a>
                    @if($currentId)
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">File ID: {{ $currentId }}</p>
                    @endif
                </div>
            </div>
        @endif

        {{-- File explorer --}}
        @if($oauthServiceId)
            @livewire('oauth-file-explorer', [
                'oauthServiceId' => $oauthServiceId,
                'fieldName' => $statePath,
                'acceptedMimeTypes' => $getAcceptedMimeTypes()
            ], key($statePath . '-' . $oauthServiceId))
        @else
            <div class="p-4 bg-yellow-50 dark:bg-yellow-100 border border-yellow-200 dark:border-yellow-300 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-700 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-yellow-800 dark:text-yellow-900">
                            {{ $serviceDisplayName }} service required
                        </p>
                        <p class="text-xs text-yellow-700 dark:text-yellow-800 mt-1">
                            @if($oauthServiceField)
                                Please select a {{ $serviceDisplayName }} service in the "{{ $oauthServiceField }}" field first.
                            @else
                                Please configure the OAuth service field for this file explorer.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        @endif

        {{-- Debug info --}}
        @if(app()->environment('local'))
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
                <strong>Debug:</strong><br>
                State Path: {{ $statePath }}<br>
                Current Value: {{ $currentUrl ?: 'empty' }}<br>
                File ID: {{ $currentId ?: 'none' }}<br>
                OAuth Service ID: {{ $oauthServiceId ?: 'none' }}<br>
                Expected Event Name: fileSelected-{{ str_replace('.', '-', $statePath) }}<br>

                <button type="button" 
                        onclick="window.dispatchEvent(new CustomEvent('fileSelected-{{ str_replace('.', '-', $statePath) }}', { detail: { fileUrl: 'https://test.com/test.pdf', fileId: 'abc123' } }))"
                        class="mt-1 px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded">
                    Test Event
                </button>
            </div>
        @endif
    </div>
</x-dynamic-component>
