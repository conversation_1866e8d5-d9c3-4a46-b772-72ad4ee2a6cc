<?php

namespace Tests\Fakes;

/**
 * Modelo falso que simula un curso para tests de linkables.
 */
class FakeCourse
{
    public $id;
    public $title;
    public $duration;

    public function __construct($id = 1, $title = 'Test Course', $duration = 120)
    {
        $this->id = $id;
        $this->title = $title;
        $this->duration = $duration;
    }

    public function getKey()
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getDuration(): int
    {
        return $this->duration;
    }
}
