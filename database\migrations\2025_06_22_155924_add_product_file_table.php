<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tabla pivote para muchos-a-muchos: product <-> file
        // Schema::create('file_product', function (Blueprint $table) {
        //     $table->foreignId('file_id')->constrained()->cascadeOnDelete();
        //     $table->foreignId('product_id')->constrained()->cascadeOnDelete();
        //     $table->primary(['file_id', 'product_id']);
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::dropIfExists('file_product');
    }
};
