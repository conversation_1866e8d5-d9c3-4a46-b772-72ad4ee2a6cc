<?php

namespace App\Filament\Resources\Ecommerce;

use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use LBCDev\Ecommerce\Models\Category;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use App\Filament\Resources\Ecommerce\CategoryResource\Pages\ListCategories;
use App\Filament\Resources\Ecommerce\CategoryResource\Pages\CreateCategory;
use App\Filament\Resources\Ecommerce\CategoryResource\Pages\EditCategory;

class CategoryResource extends Resource
{
    protected static ?string $model = Category::class;
    protected static ?string $navigationIcon = 'heroicon-o-tag';
    protected static ?string $navigationGroup = 'E-commerce';
    protected static null|int $navigationSort = 4;
    protected static bool $shouldRegisterNavigation = true;
    protected static ?string $modelLabel = 'Categoría';
    protected static ?string $pluralModelLabel = 'Categorías';
    protected static ?string $navigationLabel = 'Categorías';
    protected static ?string $recordTitleAttribute = 'name';

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()
            ->where('type', config('lbcdev-ecommerce.categories_taxonomy_type'))
            ->where('parent_id', '!=', null)
        ;
    }


    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')->required()->label('Nombre'),
            TextInput::make('slug')->label('Slug')->unique(ignoreRecord: true),
            Select::make('parent_id')
                ->label('Categoría padre')
                ->relationship('parent', 'name', function ($query) {
                    return $query
                        ->where('type', config('lbcdev-ecommerce.categories_taxonomy_type'))
                        ->where('parent_id', '!=', null)
                        ->where('slug', '!=', config('lbcdev-ecommerce.categories_taxonomy_type'));
                })
                ->preload()
                ->searchable()
                ->nullable(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->label('Nombre')->searchable(),
                TextColumn::make('slug')->label('Slug'),
                // TextColumn::make('sort_order')->label('Orden')->numeric(),
                TextColumn::make('parent.name')
                    ->label('Categoría Superior')
                    ->badge()
                    ->getStateUsing(function (Category $record) {
                        return $record->parent_id === Category::root()->id ? '' : $record->parent->name;
                    })
                    ->toggleable(),
            ])
            ->reorderable('sort_order');
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCategories::route('/'),
            'create' => CreateCategory::route('/create'),
            'edit' => EditCategory::route('/{record}/edit'),
        ];
    }
}
