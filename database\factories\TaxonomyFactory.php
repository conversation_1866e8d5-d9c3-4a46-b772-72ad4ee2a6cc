<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class TaxonomyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            'type' => 'category',
            'description' => fake()->sentence(),
        ];
    }

    public function withChildrenTaxonomies(int $count = 3)
    {
        return $this->has(
            TaxonomyFactory::new()
                ->count($count)
                ->sequence(
                    ...array_map(fn($i) => ['type' => 'category'], range(1, $count))
                )
        );
    }
}
