<?php

namespace App\Filament\Resources\Ecommerce;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use LBCDev\Ecommerce\Models\ProductVariant;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\ProductVariantResource\Pages;
use App\Filament\Resources\ProductVariantResource\RelationManagers;
use App\Filament\Resources\Ecommerce\ProductVariantResource\Pages\ListProductVariants;
use App\Filament\Resources\Ecommerce\ProductVariantResource\Pages\CreateProductVariant;
use App\Filament\Resources\Ecommerce\ProductVariantResource\Pages\EditProductVariant;

class ProductVariantResource extends Resource
{
    protected static ?string $model = ProductVariant::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'E-commerce';
    protected static null|int $navigationSort = 7;
    protected static bool $shouldRegisterNavigation = true;
    protected static ?string $modelLabel = 'Variante';
    protected static ?string $pluralModelLabel = 'Variantes';
    protected static ?string $navigationLabel = 'Variantes';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('product_id')
                    ->relationship('product', 'name')
                    ->required(),
                TextInput::make('sku')->required(),
                TextInput::make('price')->numeric(),
                TextInput::make('tax_rate')->numeric(),
                TextInput::make('attributes')->json(), // o crea un repeater
                Toggle::make('is_active'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('product.name'),
                TextColumn::make('sku'),
                TextColumn::make('price')->money(config('ecommerce.currency')),
                TextColumn::make('is_active')->boolean(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProductVariants::route('/'),
            'create' => CreateProductVariant::route('/create'),
            'edit' => EditProductVariant::route('/{record}/edit'),
        ];
    }
}
