<?php

namespace App\Filament\Resources\Ecommerce\ProductVariantResource\Pages;

use App\Filament\Resources\Ecommerce\ProductVariantResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditProductVariant extends EditRecord
{
    protected static string $resource = ProductVariantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
