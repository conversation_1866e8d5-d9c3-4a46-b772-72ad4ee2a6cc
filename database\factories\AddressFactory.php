<?php

namespace LBCDev\Ecommerce\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Foundation\Auth\User;

class AddressFactory extends Factory
{
    protected $model = \LBCDev\Ecommerce\Models\Address::class;

    public function definition(): array
    {
        $user = User::all()->random();

        if (!$user) {
            $user = User::factory()->create();
        }

        return [
            'user_id' => $user->id,
            'line1' => $this->faker->streetAddress(),
            'line2' => $this->faker->secondaryAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'country' => $this->faker->country(),
            'zip' => $this->faker->postcode(),
        ];
    }
}
