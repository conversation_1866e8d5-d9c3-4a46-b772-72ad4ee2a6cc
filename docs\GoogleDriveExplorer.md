# Google Drive Explorer Field for Filament

Este campo personalizado de Filament permite a los usuarios explorar y seleccionar archivos directamente desde Google Drive dentro de formularios de Filament.

## Características

- ✅ Navegación completa por carpetas de Google Drive
- ✅ Búsqueda de archivos por nombre
- ✅ Breadcrumb navigation para navegación fácil
- ✅ Selección de archivos con vista previa
- ✅ Integración completa con el sistema OAuth existente
- ✅ Filtros por tipo de archivo (imágenes, documentos, videos, audio)
- ✅ Interfaz responsive y accesible

## Instalación

El campo ya está incluido en el proyecto. Los archivos principales son:

- `app/Filament/Forms/Components/GoogleDriveExplorer.php` - Componente principal del campo
- `app/Livewire/GoogleDriveExplorer.php` - Componente Livewire para la interfaz
- `app/Services/GoogleDriveService.php` - Servicio para operaciones de Google Drive
- `app/Http/Controllers/GoogleDriveController.php` - Controlador API
- `resources/views/livewire/google-drive-explorer.blade.php` - Vista del explorador
- `resources/views/filament/forms/components/google-drive-explorer.blade.php` - Vista del campo

## Uso Básico

```php
use App\Filament\Forms\Components\GoogleDriveExplorer;

GoogleDriveExplorer::make('url')
    ->label('Archivo de Google Drive')
    ->oauthServiceField('oauth_service_id')
    ->required()
```

## Configuración Avanzada

### Filtrar por tipos de archivo

```php
// Solo imágenes
GoogleDriveExplorer::make('image_url')
    ->acceptImages()
    ->oauthServiceField('oauth_service_id')

// Solo documentos
GoogleDriveExplorer::make('document_url')
    ->acceptDocuments()
    ->oauthServiceField('oauth_service_id')

// Solo videos
GoogleDriveExplorer::make('video_url')
    ->acceptVideos()
    ->oauthServiceField('oauth_service_id')

// Solo audio
GoogleDriveExplorer::make('audio_url')
    ->acceptAudio()
    ->oauthServiceField('oauth_service_id')

// Tipos MIME personalizados
GoogleDriveExplorer::make('custom_url')
    ->acceptedMimeTypes(['application/pdf', 'image/jpeg'])
    ->oauthServiceField('oauth_service_id')
```

### Opciones adicionales

```php
GoogleDriveExplorer::make('url')
    ->oauthServiceField('oauth_service_id')
    ->showSelectedFileInfo(true) // Mostrar información del archivo seleccionado
    ->allowFolderSelection(false) // Permitir selección de carpetas
    ->placeholder('Selecciona un archivo de Google Drive')
    ->helperText('Navega por tus archivos de Google Drive y selecciona uno')
```

## Ejemplo Completo en un Resource

```php
<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Forms\Components\GoogleDriveExplorer;

class DocumentResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required(),

                Forms\Components\Select::make('oauth_service_id')
                    ->relationship('oauthService', 'name', 
                        fn($query) => $query->where('is_active', true)
                                           ->where('access_token', '!=', null)
                    )
                    ->live()
                    ->required()
                    ->label('Google Drive Service'),

                GoogleDriveExplorer::make('file_url')
                    ->label('Documento')
                    ->oauthServiceField('oauth_service_id')
                    ->acceptDocuments()
                    ->required()
                    ->helperText('Selecciona un documento de Google Drive'),

                Forms\Components\Textarea::make('description'),
            ]);
    }
}
```

## Requisitos

1. **OAuth Service configurado**: Debe existir al menos un servicio OAuth de Google Drive activo
2. **Token de acceso válido**: El servicio OAuth debe tener un token de acceso válido
3. **Permisos de Google Drive**: El token debe tener permisos para leer archivos de Google Drive

## API Endpoints

El campo utiliza los siguientes endpoints API:

- `POST /api/google-drive/list-files` - Listar archivos en una carpeta
- `POST /api/google-drive/get-file` - Obtener información de un archivo
- `POST /api/google-drive/breadcrumb` - Obtener breadcrumb de navegación
- `POST /api/google-drive/search` - Buscar archivos
- `POST /api/google-drive/test-connection` - Probar conexión

## Eventos Livewire

El componente emite el siguiente evento:

- `fileSelected` - Se emite cuando se selecciona un archivo
  - `fieldName`: Nombre del campo que debe actualizarse
  - `fileUrl`: URL del archivo seleccionado
  - `fileName`: Nombre del archivo seleccionado

## Personalización

### Estilos CSS

El campo utiliza las clases CSS de Tailwind incluidas en Filament. Puedes personalizar los estilos modificando las vistas:

- `resources/views/livewire/google-drive-explorer.blade.php`
- `resources/views/filament/forms/components/google-drive-explorer.blade.php`

### Iconos de archivos

Los iconos se determinan automáticamente basándose en el tipo MIME del archivo. Puedes personalizar la lógica en el método `getFileIcon()` del componente Livewire.

## Troubleshooting

### Error: "OAuth service not configured"
- Verifica que el servicio OAuth esté activo
- Asegúrate de que tenga un token de acceso válido

### Error: "Drive service not initialized"
- Verifica las credenciales de Google Drive
- Comprueba que los permisos sean correctos

### Los archivos no se cargan
- Verifica la conexión a internet
- Comprueba que el token no haya expirado
- Revisa los logs de Laravel para errores específicos

## Seguridad

- Los tokens de acceso se manejan de forma segura a través del sistema OAuth
- Las operaciones de Google Drive se realizan server-side
- Se validan todos los parámetros de entrada en los endpoints API
- Se utilizan las mejores prácticas de seguridad de Filament
