<?php

namespace App\Policies;

use App\Models\Product;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProductPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // return $user->hasPermissionTo('view products') || $user->hasRole('admin');
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Product $product): bool
    {
        // return $user->hasPermissionTo('view products') || $user->hasRole('admin');
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // return $user->hasPermissionTo('create products') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Product $product): bool
    {
        // return $user->hasPermissionTo('edit products') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Product $product): bool
    {
        // return $user->hasPermissionTo('delete products') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Product $product): bool
    {
        // return $user->hasPermissionTo('restore products') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Product $product): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can manage product categories.
     */
    public function manageCategories(User $user): bool
    {
        // return $user->hasPermissionTo('manage product categories') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can manage product tags.
     */
    public function manageTags(User $user): bool
    {
        // return $user->hasPermissionTo('manage product tags') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can bulk update products.
     */
    public function bulkUpdate(User $user): bool
    {
        // return $user->hasPermissionTo('bulk edit products') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can export products.
     */
    public function export(User $user): bool
    {
        // return $user->hasPermissionTo('export products') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can import products.
     */
    public function import(User $user): bool
    {
        // return $user->hasPermissionTo('import products') || $user->hasRole('admin');
        return $user->hasRole('admin');
    }
}
