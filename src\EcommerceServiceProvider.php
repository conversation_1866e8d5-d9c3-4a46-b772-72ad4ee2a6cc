<?php

namespace LBCDev\Ecommerce;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\Login;
use Illuminate\Console\Scheduling\Schedule;
use LBCDev\Ecommerce\Providers\CartServiceProvider;
use LBCDev\Ecommerce\Events\UserLoggedIn;
use LBCDev\Ecommerce\Services\LinkableRegistry;

class EcommerceServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // Merge configuration
        $this->mergeConfigFrom(
            __DIR__ . '/../config/ecommerce.php',
            'lbcdev-ecommerce'
        );

        // Register internal service providers
        $this->app->register(CartServiceProvider::class);

        // Register facade
        $this->app->singleton('ecommerce', function () {
            return new \stdClass(); // Placeholder for facade
        });
    }

    public function boot(): void
    {
        // Load resources
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'ecommerce');
        $this->loadTranslationsFrom(__DIR__ . '/../resources/lang', 'ecommerce');

        // Publishable resources
        $this->publishes([
            __DIR__ . '/../config/ecommerce.php' => config_path('lbcdev-ecommerce.php'),
        ], 'lbcdev-ecommerce-config');

        $this->publishes([
            __DIR__ . '/../resources/views' => resource_path('views/vendor/lbcdev/ecommerce'),
        ], 'lbcdev-ecommerce-views');

        $this->publishes([
            __DIR__ . '/../public' => public_path('vendor/lbcdev/ecommerce'),
        ], 'lbcdev-ecommerce-assets');

        // Register event listeners
        Event::listen(Login::class, UserLoggedIn::class);

        // Optional: schedule cart cleanup
        if (config('lbcdev-ecommerce.cart.auto_cleanup', true)) {
            $this->app->booted(function () {
                $this->app->make(Schedule::class)
                    ->command('ecommerce:cleanup-carts')
                    ->daily();
            });
        }

        // Load linkables from configuration
        LinkableRegistry::loadFromConfig();
    }
}
