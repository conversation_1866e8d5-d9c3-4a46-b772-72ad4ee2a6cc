[2025-09-09 09:18:54] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 09:18:54] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 09:22:32] local.ERROR: Command "livewire:list" is not defined.

Did you mean one of these?
    channel:list
    event:list
    horizon:list
    livewire:attribute
    livewire:configure-s3-upload-cleanup
    livewire:copy
    livewire:delete
    livewire:form
    livewire:layout
    livewire:make
    livewire:move
    livewire:publish
    livewire:stubs
    livewire:upgrade
    make:infolist-entry
    make:infolist-layout
    make:listener
    queue:listen
    route:list
    schedule:list {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"livewire:list\" is not defined.

Did you mean one of these?
    channel:list
    event:list
    horizon:list
    livewire:attribute
    livewire:configure-s3-upload-cleanup
    livewire:copy
    livewire:delete
    livewire:form
    livewire:layout
    livewire:make
    livewire:move
    livewire:publish
    livewire:stubs
    livewire:upgrade
    make:infolist-entry
    make:infolist-layout
    make:listener
    queue:listen
    route:list
    schedule:list at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('livewire:list')
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-09-09 09:23:34] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 09:23:34] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 09:36:59] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 09:36:59] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 09:39:20] local.ERROR: Unable to find component: [test-counter] {"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [test-counter] at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php:116)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('test-counter')
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('test-counter', NULL)
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(43): Livewire\\LivewireManager->new('test-counter')
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('test-counter', Array, 'lw-1491464798-0')
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('test-counter', Array, 'lw-1491464798-0')
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\348a1fca40d63a31d0dc72413cd04405.php(46): Livewire\\Volt\\LivewireManager->mount('test-counter', Array, 'lw-1491464798-0', Array, Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Proyectos\\\\Cl...')
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Proyectos\\\\Cl...', Array)
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\boost\\src\\Middleware\\InjectBoost.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Laravel\\Boost\\Middleware\\InjectBoost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#74 {main}
"} 
[2025-09-09 09:40:16] local.ERROR: Unable to find component: [test.counter] {"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [test.counter] at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php:116)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('test.counter')
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('test.counter', NULL)
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(43): Livewire\\LivewireManager->new('test.counter')
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('test.counter', Array, 'lw-1491464798-0')
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('test.counter', Array, 'lw-1491464798-0')
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\348a1fca40d63a31d0dc72413cd04405.php(46): Livewire\\Volt\\LivewireManager->mount('test.counter', Array, 'lw-1491464798-0', Array, Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Proyectos\\\\Cl...')
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Proyectos\\\\Cl...', Array)
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\boost\\src\\Middleware\\InjectBoost.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Laravel\\Boost\\Middleware\\InjectBoost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#74 {main}
"} 
[2025-09-09 09:41:30] local.ERROR: Unable to find component: [test-counter] {"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [test-counter] at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php:116)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('test-counter')
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('test-counter', NULL)
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(43): Livewire\\LivewireManager->new('test-counter')
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('test-counter', Array, 'lw-1491464798-0')
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('test-counter', Array, 'lw-1491464798-0')
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\348a1fca40d63a31d0dc72413cd04405.php(46): Livewire\\Volt\\LivewireManager->mount('test-counter', Array, 'lw-1491464798-0', Array, Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Proyectos\\\\Cl...')
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Proyectos\\\\Cl...', Array)
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\boost\\src\\Middleware\\InjectBoost.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Laravel\\Boost\\Middleware\\InjectBoost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#74 {main}
"} 
[2025-09-09 09:43:23] local.ERROR: Unable to find component: [test.counter] {"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [test.counter] at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php:116)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('test.counter')
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('test.counter', NULL)
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(43): Livewire\\LivewireManager->new('test.counter')
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('test.counter', Array, 'lw-1491464798-0')
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('test.counter', Array, 'lw-1491464798-0')
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\348a1fca40d63a31d0dc72413cd04405.php(46): Livewire\\Volt\\LivewireManager->mount('test.counter', Array, 'lw-1491464798-0', Array, Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Proyectos\\\\Cl...')
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Proyectos\\\\Cl...', Array)
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\boost\\src\\Middleware\\InjectBoost.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Laravel\\Boost\\Middleware\\InjectBoost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#74 {main}
"} 
[2025-09-09 09:44:06] local.ERROR: Undefined variable $text (View: C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views\livewire\test\counter.blade.php) (View: C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views\livewire\test\counter.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Undefined variable $text (View: C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\resources\\views\\livewire\\test\\counter.blade.php) (View: C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\resources\\views\\livewire\\test\\counter.blade.php) at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\67e8207dd9d1d9cfd0e98a3dc40ad326.php:13)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\boost\\src\\Middleware\\InjectBoost.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Laravel\\Boost\\Middleware\\InjectBoost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#67 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): Undefined variable $text (View: C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\resources\\views\\livewire\\test\\counter.blade.php) at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\67e8207dd9d1d9cfd0e98a3dc40ad326.php:13)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ErrorException), 3)
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(40): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(ErrorException), 3)
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(259): Illuminate\\View\\View->render(Object(Closure))
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(303): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(251): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Test\\Counter), Object(Closure))
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Test\\Counter), '<div></div>')
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('test.counter', Array, 'lw-1491464798-0')
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('test.counter', Array, 'lw-1491464798-0')
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\348a1fca40d63a31d0dc72413cd04405.php(46): Livewire\\Volt\\LivewireManager->mount('test.counter', Array, 'lw-1491464798-0', Array, Array)
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Proyectos\\\\Cl...')
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Proyectos\\\\Cl...', Array)
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#25 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\boost\\src\\Middleware\\InjectBoost.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Laravel\\Boost\\Middleware\\InjectBoost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#82 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $text at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\67e8207dd9d1d9cfd0e98a3dc40ad326.php:13)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\Proyectos\\\\Cl...', 13)
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\67e8207dd9d1d9cfd0e98a3dc40ad326.php(13): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\Proyectos\\\\Cl...', 13)
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Proyectos\\\\Cl...')
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Test\\Counter->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(259): Illuminate\\View\\View->render(Object(Closure))
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(303): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(251): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Test\\Counter), Object(Closure))
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Test\\Counter), '<div></div>')
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('test.counter', Array, 'lw-1491464798-0')
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('test.counter', Array, 'lw-1491464798-0')
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\storage\\framework\\views\\348a1fca40d63a31d0dc72413cd04405.php(46): Livewire\\Volt\\LivewireManager->mount('test.counter', Array, 'lw-1491464798-0', Array, Array)
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Proyectos\\\\Cl...')
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Proyectos\\\\Cl...', Array)
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Proyectos\\\\Cl...', Array)
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Proyectos\\\\Cl...', Array)
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#25 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationViewPath->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\boost\\src\\Middleware\\InjectBoost.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Laravel\\Boost\\Middleware\\InjectBoost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#47 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#55 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#56 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#57 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#58 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#81 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#82 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#83 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#84 {main}
"} 
[2025-09-09 10:02:48] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 10:02:48] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:05:14] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 10:05:14] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:05:14] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 10:05:14] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:05:37] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 10:05:37] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:05:42] local.INFO: testMethod called - Livewire is working  
[2025-09-09 10:05:42] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:08:47] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 10:08:47] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:10:06] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 10:10:06] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:19:02] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 10:19:02] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:20:40] local.INFO: ProductList mounted {"sortBy":"name","sortDirection":"asc"} 
[2025-09-09 10:20:40] local.INFO: Query being executed {"sortBy":"name","sortDirection":"asc","search":""} 
[2025-09-09 10:35:32] local.ERROR: Call to undefined method Livewire\LivewireManager::setAssetUrl() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setAssetUrl() at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:363)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Providers\\AppServiceProvider.php(36): Illuminate\\Support\\Facades\\Facade::__callStatic('setAssetUrl', Array)
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 'App\\\\Providers\\\\A...')
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#17 {main}
"} 
[2025-09-09 10:37:51] local.ERROR: Class "App\Providers\Livewire" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\Livewire\" not found at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Providers\\AppServiceProvider.php:35)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 'App\\\\Providers\\\\A...')
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#16 {main}
"} 
[2025-09-09 10:37:58] local.ERROR: Class "App\Providers\Livewire" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\Livewire\" not found at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Providers\\AppServiceProvider.php:35)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 'App\\\\Providers\\\\A...')
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#16 {main}
"} 
[2025-09-09 10:38:26] local.ERROR: Class "App\Providers\Livewire" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\Livewire\" not found at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Providers\\AppServiceProvider.php:35)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 'App\\\\Providers\\\\A...')
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#16 {main}
"} 
[2025-09-09 10:38:29] local.ERROR: Call to undefined method Livewire\LivewireManager::setAssetUrl() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setAssetUrl() at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:363)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Providers\\AppServiceProvider.php(36): Illuminate\\Support\\Facades\\Facade::__callStatic('setAssetUrl', Array)
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 'App\\\\Providers\\\\A...')
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#17 {main}
"} 
[2025-09-09 10:42:32] local.ERROR: Call to undefined method Livewire\LivewireManager::routes() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::routes() at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:363)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\routes\\web.php(22): Illuminate\\Support\\Facades\\Facade::__callStatic('routes', Array)
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(524): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Routing\\Router))
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\routes\\web.php(21): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(34): require('C:\\\\Proyectos\\\\Cl...')
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(526): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\Proyectos\\\\Cl...')
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes('C:\\\\Proyectos\\\\Cl...')
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, 'C:\\\\Proyectos\\\\Cl...')
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(248): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\Proyectos\\\\Cl...')
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call(Object(Closure))
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(143): Illuminate\\Container\\Container->call(Object(Closure))
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1153): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider))
#25 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider), 'Illuminate\\\\Foun...')
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
[2025-09-09 10:42:35] local.ERROR: Call to undefined method Livewire\LivewireManager::routes() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::routes() at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:363)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\routes\\web.php(22): Illuminate\\Support\\Facades\\Facade::__callStatic('routes', Array)
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(524): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Routing\\Router))
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\routes\\web.php(21): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(34): require('C:\\\\Proyectos\\\\Cl...')
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(526): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\Proyectos\\\\Cl...')
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes('C:\\\\Proyectos\\\\Cl...')
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, 'C:\\\\Proyectos\\\\Cl...')
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(248): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\Proyectos\\\\Cl...')
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call(Object(Closure))
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(143): Illuminate\\Container\\Container->call(Object(Closure))
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1153): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider))
#25 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider), 'Illuminate\\\\Foun...')
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
[2025-09-09 10:42:38] local.ERROR: Call to undefined method Livewire\LivewireManager::routes() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::routes() at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:363)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\routes\\web.php(22): Illuminate\\Support\\Facades\\Facade::__callStatic('routes', Array)
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(524): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Routing\\Router))
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\routes\\web.php(21): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(34): require('C:\\\\Proyectos\\\\Cl...')
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(526): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\Proyectos\\\\Cl...')
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes('C:\\\\Proyectos\\\\Cl...')
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, 'C:\\\\Proyectos\\\\Cl...')
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(248): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\Proyectos\\\\Cl...')
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call(Object(Closure))
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#21 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#22 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(143): Illuminate\\Container\\Container->call(Object(Closure))
#23 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1153): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#24 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider))
#25 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider), 'Illuminate\\\\Foun...')
#26 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#27 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#28 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#29 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#30 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#31 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
