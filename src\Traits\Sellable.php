<?php

namespace LBCDev\Ecommerce\Traits;

/**
 * Trait Sellable
 *
 * Allows a model to be treated as a sellable item in the e-commerce system.
 *
 * @property string|null $title
 * @property string|null $name
 * @property float|null $price
 * @property float|null $tax_rate
 */
trait Sellable
{
    /**
     * Get the display name of the item.
     *
     * @return string
     */
    public function getSellableName(): string
    {
        return $this->getAttribute('title')
            ?? $this->getAttribute('name')
            ?? 'Unnamed Item';
    }

    /**
     * Get the unique identifier of the item.
     *
     * @return mixed
     */
    public function getSellableId(): mixed
    {
        return $this->getKey();
    }

    /**
     * Get the type (class name) of the item.
     *
     * @return string
     */
    public function getSellableType(): string
    {
        return class_basename($this);
    }

    /**
     * Get the base price (without taxes).
     *
     * @return float
     */
    public function getSellablePrice(): float
    {
        return (float) ($this->getAttribute('price') ?? 0);
    }

    /**
     * Get the tax percentage.
     *
     * @return float
     */
    public function getSellableTaxRate(): float
    {
        return (float) ($this->getAttribute('tax_rate') ?? config('ecommerce.tax_rate', 0));
    }

    /**
     * Get the total price including tax.
     *
     * @return float
     */
    public function getSellableTotalPrice(): float
    {
        return $this->getSellablePrice() * (1 + $this->getSellableTaxRate() / 100);
    }
}
