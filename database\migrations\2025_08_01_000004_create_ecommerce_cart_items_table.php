<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('lbcdev_ecommerce_cart_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cart_id')->constrained('lbcdev_ecommerce_carts')->onDelete('cascade');

            // Relación polimórfica con el item vendible
            $table->string('sellable_type');
            $table->unsignedBigInteger('sellable_id');

            // Datos del item
            $table->string('name');
            $table->integer('quantity');
            $table->decimal('unit_price', 10, 2);
            $table->decimal('tax_rate', 5, 2);

            $table->timestamps();

            // Índices
            $table->index(['sellable_type', 'sellable_id']);
            $table->index('cart_id');

            // Prevenir duplicados
            $table->unique(['cart_id', 'sellable_type', 'sellable_id'], 'cart_items_unique_sellable');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('lbcdev_ecommerce_cart_items');
    }
};
