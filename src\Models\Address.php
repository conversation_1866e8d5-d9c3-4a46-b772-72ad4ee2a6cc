<?php

namespace LBCDev\Ecommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use LBCDev\Ecommerce\Database\Factories\AddressFactory;

class Address extends Model
{
    use HasFactory;

    protected $table = 'lbcdev_ecommerce_addresses';

    protected $fillable = [
        'line1',
        'line2',
        'city',
        'state',
        'country',
        'zip',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(config('auth.providers.users.model'));
    }

    public function getFullAddressAttribute(): string
    {
        return implode(', ', array_filter([
            $this->line1,
            $this->line2,
            $this->city,
            $this->state,
            $this->country,
            $this->zip,
        ]));
    }

    protected static function newFactory()
    {
        return AddressFactory::new();
    }
}
