<?php

namespace Tests\Feature;

use App\Services\PaymentGatewayFactory;
use Tests\TestCase;

class OmnipayIntegrationTest extends TestCase
{
    public function test_can_create_stripe_gateway_with_test_credentials(): void
    {
        // Set test credentials
        config([
            'payments.gateways.stripe.enabled' => true,
            'payments.gateways.stripe.driver' => 'Stripe',
            'payments.gateways.stripe.options.apiKey' => 'sk_test_fake_key_for_testing',
        ]);

        /** @var PaymentGatewayFactory $factory */
        $factory = $this->app->make('omnipay');

        $gateway = $factory->create('stripe');

        $this->assertInstanceOf(\Omnipay\Common\GatewayInterface::class, $gateway);
        $this->assertEquals('Stripe Charge', $gateway->getName());

        // Verify the gateway has the test API key
        $this->assertEquals('sk_test_fake_key_for_testing', $gateway->getApiKey());
    }

    public function test_can_create_paypal_gateway_with_test_credentials(): void
    {
        // Set test credentials
        config([
            'payments.gateways.paypal.enabled' => true,
            'payments.gateways.paypal.driver' => 'PayPal_Express',
            'payments.gateways.paypal.options.username' => 'test_username',
            'payments.gateways.paypal.options.password' => 'test_password',
            'payments.gateways.paypal.options.testMode' => true,
        ]);

        /** @var PaymentGatewayFactory $factory */
        $factory = $this->app->make('omnipay');

        $gateway = $factory->create('paypal');

        $this->assertInstanceOf(\Omnipay\Common\GatewayInterface::class, $gateway);
        $this->assertEquals('PayPal Express', $gateway->getName());

        // Verify the gateway is in test mode
        $this->assertTrue($gateway->getTestMode());
        $this->assertEquals('test_username', $gateway->getUsername());
    }

    public function test_omnipay_gateways_support_required_methods(): void
    {
        config([
            'payments.gateways.stripe.enabled' => true,
            'payments.gateways.stripe.driver' => 'Stripe',
            'payments.gateways.stripe.options.apiKey' => 'sk_test_fake_key',
        ]);

        /** @var PaymentGatewayFactory $factory */
        $factory = $this->app->make('omnipay');
        $gateway = $factory->create('stripe');

        // Check that the gateway supports the methods we need
        $this->assertTrue($gateway->supportsPurchase());
        $this->assertTrue($gateway->supportsCompletePurchase());

        // These methods should exist (even if they return false)
        $this->assertIsBool($gateway->supportsRefund());
        $this->assertIsBool($gateway->supportsVoid());
    }

    public function test_can_prepare_purchase_request_structure(): void
    {
        config([
            'payments.gateways.stripe.enabled' => true,
            'payments.gateways.stripe.driver' => 'Stripe',
            'payments.gateways.stripe.options.apiKey' => 'sk_test_fake_key',
        ]);

        /** @var PaymentGatewayFactory $factory */
        $factory = $this->app->make('omnipay');
        $gateway = $factory->create('stripe');

        // Create a purchase request (don't send it)
        $request = $gateway->purchase([
            'amount' => '10.00',
            'currency' => 'EUR',
            'description' => 'Test payment',
            'returnUrl' => 'https://example.com/success',
            'cancelUrl' => 'https://example.com/cancel',
        ]);

        $this->assertInstanceOf(\Omnipay\Common\Message\RequestInterface::class, $request);

        // Verify the request has the expected data
        $this->assertEquals('10.00', $request->getAmount());
        $this->assertEquals('EUR', $request->getCurrency());
        $this->assertEquals('Test payment', $request->getDescription());
    }

    public function test_payment_urls_configuration_is_accessible(): void
    {
        $successUrl = config('payments.urls.success');
        $cancelUrl = config('payments.urls.cancel');
        $notifyUrl = config('payments.urls.notify');
        $webhookUrl = config('payments.urls.webhook');

        $this->assertEquals('/payment/success', $successUrl);
        $this->assertEquals('/payment/cancel', $cancelUrl);
        $this->assertEquals('/payment/notify', $notifyUrl);
        $this->assertEquals('/payment/webhook', $webhookUrl);

        // Test that we can build full URLs
        $fullSuccessUrl = url($successUrl);
        $this->assertStringContainsString('payment/success', $fullSuccessUrl);
    }

    public function test_payment_configuration_has_all_required_keys(): void
    {
        $config = config('payments');

        // Check main configuration keys exist
        $this->assertArrayHasKey('default', $config);
        $this->assertArrayHasKey('currency', $config);
        $this->assertArrayHasKey('gateways', $config);
        $this->assertArrayHasKey('urls', $config);
        $this->assertArrayHasKey('settings', $config);
        $this->assertArrayHasKey('currencies', $config);

        // Check gateway configurations
        $this->assertArrayHasKey('stripe', $config['gateways']);
        $this->assertArrayHasKey('paypal', $config['gateways']);
        $this->assertArrayHasKey('redsys', $config['gateways']);

        // Check each gateway has required keys
        foreach (['stripe', 'paypal'] as $gateway) {
            $gatewayConfig = $config['gateways'][$gateway];
            $this->assertArrayHasKey('driver', $gatewayConfig);
            $this->assertArrayHasKey('options', $gatewayConfig);
            $this->assertArrayHasKey('enabled', $gatewayConfig);
        }
    }
}
