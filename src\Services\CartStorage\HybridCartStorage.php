<?php

namespace LBCDev\Ecommerce\Services\CartStorage;

use LBCDev\Ecommerce\Contracts\CartStorageInterface;
use LBCDev\Ecommerce\Models\Cart;
use Illuminate\Support\Facades\Auth;

class HybridCartStorage implements CartStorageInterface
{
    protected SessionCartStorage $sessionStorage;
    protected DatabaseCartStorage $databaseStorage;

    public function __construct(
        SessionCartStorage $sessionStorage,
        DatabaseCartStorage $databaseStorage
    ) {
        $this->sessionStorage = $sessionStorage;
        $this->databaseStorage = $databaseStorage;
    }

    public function getCart(string $identifier): Cart
    {
        if ($this->shouldUsePersistentStorage($identifier)) {
            return $this->databaseStorage->getCart($identifier);
        }

        return $this->sessionStorage->getCart($identifier);
    }

    public function saveCart(string $identifier, Cart $cart): void
    {
        if ($this->shouldUsePersistentStorage($identifier)) {
            $this->databaseStorage->saveCart($identifier, $cart);
        } else {
            $this->sessionStorage->saveCart($identifier, $cart);
        }
    }

    public function clearCart(string $identifier): void
    {
        // Limpiar en ambos storages por seguridad
        $this->sessionStorage->clearCart($identifier);
        $this->databaseStorage->clearCart($identifier);
    }

    public function hasCart(string $identifier): bool
    {
        if ($this->shouldUsePersistentStorage($identifier)) {
            return $this->databaseStorage->hasCart($identifier);
        }

        return $this->sessionStorage->hasCart($identifier);
    }

    public function mergeCarts(string $fromIdentifier, string $toIdentifier): Cart
    {
        // Obtener carritos de ambos storages
        $sessionCart = $this->sessionStorage->getCart($fromIdentifier);
        $databaseCart = $this->databaseStorage->getCart($toIdentifier);

        // Fusionar en memoria
        foreach ($sessionCart->getItems() as $item) {
            $databaseCart->addItem($item->getItem(), $item->getQuantity());
        }

        // Guardar resultado en el storage apropiado
        $this->saveCart($toIdentifier, $databaseCart);

        // Limpiar carrito de sesión
        $this->sessionStorage->clearCart($fromIdentifier);

        return $databaseCart;
    }

    /**
     * Migrar carrito de sesión a persistente al hacer login.
     */
    public function migrateSessionToUser(string $sessionIdentifier, string $userIdentifier): Cart
    {
        return $this->mergeCarts($sessionIdentifier, $userIdentifier);
    }

    protected function shouldUsePersistentStorage(string $identifier): bool
    {
        // Si el identificador es de usuario (comienza con 'user_'), usar BD
        return str_starts_with($identifier, 'user_');
    }
}
