<?php

// database/factories/ProductVariantFactory.php
namespace LBCDev\Ecommerce\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use LBCDev\Ecommerce\Models\ProductVariant;
use LBCDev\Ecommerce\Models\Product;

class ProductVariantFactory extends Factory
{
    protected $model = ProductVariant::class;

    public function definition(): array
    {
        $product = Product::factory()->create();

        return [
            'product_id' => $product->id,
            'sku'        => $this->faker->unique()->bothify('SKU-???-###'),
            'price'      => $this->faker->randomFloat(2, 5, 100),
            'tax_rate'   => 21.0,
            'attributes' => ['size' => 'M', 'color' => 'red'],
            'is_active'  => true,
        ];
    }
}
