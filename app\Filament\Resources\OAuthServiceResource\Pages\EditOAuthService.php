<?php

namespace App\Filament\Resources\OAuthServiceResource\Pages;

use Filament\Actions;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\OAuthServiceResource;

class EditOAuthService extends EditRecord
{
    protected static string $resource = OAuthServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('authorize')
                ->label('Authorize Service')
                ->icon('heroicon-m-key')
                ->color('success')
                ->visible(fn() => !$this->record->access_token || $this->record->isTokenExpired())
                ->url(fn() => route('oauth-manager.authorize', $this->record)),

            Actions\Action::make('revoke')
                ->label('Revoke Token')
                ->icon('heroicon-m-x-circle')
                ->color('danger')
                ->visible(fn() => $this->record->access_token && !$this->record->isTokenExpired())
                ->requiresConfirmation()
                ->modalHeading('Revoke OAuth Token')
                ->modalDescription('Are you sure you want to revoke this OAuth token? This will disconnect the service and you will need to re-authorize it.')
                ->modalSubmitActionLabel('Revoke Token')
                ->action(function () {
                    $success = $this->record->revokeToken();

                    if ($success) {
                        $this->notify('success', 'Token revoked successfully');
                    } else {
                        $this->notify('warning', 'Token was cleared locally, but remote revocation may have failed');
                    }
                }),

            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // comprobar si $data['credentials'] es vacio y si lo es llenar con los valores de la configuración
        if (empty($data['credentials'])) {
            $data['credentials'] = config('oauth-manager.services.' . $data['service_type'] . '.fields');
        }

        $record->update($data);

        return $record;
    }
}
