<?php

namespace App\Filament\Resources\Ecommerce\OrderResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use LBCDev\Ecommerce\Models\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Resources\RelationManagers\RelationManager;

class ItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Hidden::make('sellable_type')
                ->default(\App\Models\Product::class)
                ->dehydrated(),

            Forms\Components\Select::make('sellable_id')
                ->label('Producto')
                ->options(Product::all()->pluck('name', 'id'))
                ->searchable()
                ->preload()
                ->required()
                ->reactive()
                ->afterStateUpdated(function ($state, callable $set) {
                    $product = Product::find($state);
                    if ($product) {
                        $set('name', $product->name);
                        $set('unit_price', $product->price ?? 0); // asumiendo que hay un campo price
                    }
                }),

            Forms\Components\TextInput::make('name')
                ->label('Nombre del producto')
                ->required(),

            Forms\Components\TextInput::make('quantity')
                ->numeric()
                ->minValue(1)
                ->required()
                ->reactive()
                ->afterStateUpdated(
                    fn($state, callable $set, callable $get) =>
                    $this->calculateTotals($set, $get)
                ),

            Forms\Components\TextInput::make('unit_price')
                ->numeric()
                ->required()
                ->reactive()
                ->afterStateUpdated(
                    fn($state, callable $set, callable $get) =>
                    $this->calculateTotals($set, $get)
                ),

            Forms\Components\TextInput::make('tax_rate')
                ->numeric()
                ->required()
                ->reactive()
                ->afterStateUpdated(
                    fn($state, callable $set, callable $get) =>
                    $this->calculateTotals($set, $get)
                ),

            Forms\Components\TextInput::make('subtotal')
                ->numeric()
                ->required(),

            Forms\Components\TextInput::make('tax_amount')
                ->label('Monto de impuesto')
                ->numeric()
                ->required(),

            Forms\Components\TextInput::make('total')
                ->numeric()
                ->required(),
        ]);
    }


    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->getStateUsing(fn($record) => $record->sellable->name)
                    ->label('Producto'),
                Tables\Columns\TextColumn::make('quantity'),
                Tables\Columns\TextColumn::make('unit_price')->money('USD', true),
                Tables\Columns\TextColumn::make('total')->money('USD', true),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    protected function calculateTotals(callable $set, callable $get): void
    {
        $qty = (int) $get('quantity');
        $price = (float) $get('unit_price');
        $taxRate = (float) $get('tax_rate');

        $subtotal = $qty * $price;
        $tax = $subtotal * ($taxRate / 100);
        $total = $subtotal + $tax;

        $set('subtotal', $subtotal);
        $set('tax_amount', $tax);
        $set('total', $total);
    }
}
