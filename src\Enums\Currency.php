<?php

namespace LBCDev\Ecommerce\Enums;

enum Currency: string
{
    case EUR = 'EUR';
    case USD = 'USD';

    public static function labels(): array
    {
        return [
            self::EUR->value => 'Euro',
            self::USD->value => 'US Dollar',
        ];
    }

    public function symbol(): string
    {
        return match ($this) {
            self::EUR => '€',
            self::USD => '$',
        };
    }

    public static function getPossibleValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
