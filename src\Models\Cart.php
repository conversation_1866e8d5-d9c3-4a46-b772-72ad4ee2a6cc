<?php

namespace LBCDev\Ecommerce\Models;

use LBCDev\Ecommerce\Models\CartItem;

class Cart
{
    /**
     * Array asociativo de CartItems, la clave será "type:id" para evitar duplicados.
     *
     * @var CartItem[]
     */
    protected array $items = [];

    /**
     * Añade un ítem vendible al carrito.
     *
     * Si el ítem ya existe, incrementa la cantidad.
     *
     * @param mixed $sellable Modelo vendible (debe usar Trait Sellable)
     * @param int $quantity
     * @return void
     */
    public function addItem($sellable, int $quantity = 1): void
    {
        $key = $this->getItemKey($sellable);

        if (isset($this->items[$key])) {
            $existing = $this->items[$key];
            $existing->setQuantity($existing->getQuantity() + $quantity);
        } else {
            $this->items[$key] = new CartItem($sellable, $quantity);
        }
    }

    /**
     * Remueve un ítem del carrito.
     *
     * @param mixed $sellable
     * @return void
     */
    public function removeItem($sellable): void
    {
        $key = $this->getItemKey($sellable);

        unset($this->items[$key]);
    }

    /**
     * Cambia la cantidad de un ítem del carrito.
     *
     * Si la cantidad es menor que 1, se remueve el ítem.
     *
     * @param mixed $sellable
     * @param int $quantity
     * @return void
     */
    public function updateQuantity($sellable, int $quantity): void
    {
        $key = $this->getItemKey($sellable);

        if (isset($this->items[$key])) {
            if ($quantity < 1) {
                unset($this->items[$key]);
            } else {
                $this->items[$key]->setQuantity($quantity);
            }
        }
    }

    /**
     * Busca un ítem en el carrito.
     *
     * @param mixed $sellable
     * @return CartItem|null
     */
    public function findItem($sellable): ?CartItem
    {
        foreach ($this->items as $item) {
            if ($item->matches($sellable)) {
                return $item;
            }
        }

        return null;
    }

    /**
     * Obtiene todos los ítems del carrito.
     *
     * @return CartItem[]
     */
    public function getItems(): array
    {
        return array_values($this->items);
    }

    /**
     * Calcula el total sin impuestos.
     *
     * @return float
     */
    public function getTotal(): float
    {
        $total = 0;

        foreach ($this->items as $item) {
            $total += $item->getSubtotal();
        }

        return $total;
    }

    /**
     * Calcula el total con impuestos.
     *
     * @return float
     */
    public function getTotalWithTax(): float
    {
        $total = 0;

        foreach ($this->items as $item) {
            $total += $item->getSubtotalWithTax();
        }

        return $total;
    }

    /**
     * Vacía el carrito.
     *
     * @return void
     */
    public function clear(): void
    {
        $this->items = [];
    }

    /**
     * Genera una clave única para identificar un ítem (type + id).
     *
     * @param mixed $sellable
     * @return string
     */
    protected function getItemKey($sellable): string
    {
        $type = $sellable->getSellableType();
        $id = $sellable->getSellableId();

        return $type . ':' . $id;
    }
}
