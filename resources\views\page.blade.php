@extends('layouts.app')

@section('content')
    @php
        $menu = \App\Models\Menu::where('name', 'principal')->first();
    @endphp

    @if($menu)
        <ul>
            @foreach($menu->items()->whereNull('parent_id')->get() as $item)
                <li><a href="{{ $item->url }}">{{ $item->label }}</a></li>
            @endforeach
        </ul>
    @endif

    <h1>{{ $page->title }}</h1>
    <div>{!! $page->content !!}</div>
@endsection

