<?php

namespace App\Filament\Resources\Ecommerce\ProductResource\Pages;

use Filament\Actions;
use LBCDev\Ecommerce\Models\Tag;
use Illuminate\Support\Facades\Log;
use LBCDev\Ecommerce\Models\Category;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\Ecommerce\ProductResource;

class EditProduct extends EditRecord
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Extraer categorías y tags
        $categories = $data['categories'] ?? [];
        $tags = $data['tags'] ?? [];

        // Remover del array de datos principales
        unset($data['categories'], $data['tags']);

        // Actualizar el modelo
        $record->update($data);

        // Validar y sincronizar categorías
        $validCategoryIds = [];
        if (!empty($categories)) {
            $validCategoryIds = Category::whereIn('id', $categories)->pluck('id')->toArray();
        }

        $validTagIds = [];
        if (!empty($tags)) {
            $validTagIds = Tag::whereIn('id', $tags)->pluck('id')->toArray();
        }

        // Sincronizar (vacío significa desasociar todo)
        $record->syncCategories($validCategoryIds);
        $record->syncTags($validTagIds);

        return $record;
    }
}
