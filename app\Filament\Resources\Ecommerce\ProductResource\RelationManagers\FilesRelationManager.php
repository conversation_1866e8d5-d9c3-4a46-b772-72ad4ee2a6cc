<?php

namespace App\Filament\Resources\Ecommerce\ProductResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\File;
use LBCDev\Ecommerce\Facades\Ecommerce;

class FilesRelationManager extends RelationManager
{
    protected static string $relationship = 'linkables';

    protected static ?string $title = 'Archivos Vinculados';

    protected static ?string $modelLabel = 'archivo';

    protected static ?string $pluralModelLabel = 'archivos';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('productable_id')
                    ->label('Archivo')
                    ->options(File::all()->pluck('nombre', 'id'))
                    ->required()
                    ->searchable(),

                Forms\Components\Select::make('purpose')
                    ->label('Propósito')
                    ->options([
                        'primary' => 'Principal',
                        'included' => 'Incluido',
                        'bonus' => 'Bonus',
                    ])
                    ->default('included')
                    ->required(),

                Forms\Components\Select::make('access_policy')
                    ->label('Política de Acceso')
                    ->options([
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                    ])
                    ->default('after_payment')
                    ->required(),

                Forms\Components\KeyValue::make('meta')
                    ->label('Metadatos')
                    ->keyLabel('Clave')
                    ->valueLabel('Valor')
                    ->addActionLabel('Añadir metadato')
                    ->helperText('Configuración adicional como límites de descarga, duración, etc.'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nombre')
            ->modifyQueryUsing(fn(Builder $query) => $query->where('productable_type', File::class))
            ->columns([
                Tables\Columns\TextColumn::make('productable.nombre')
                    ->label('Nombre del Archivo')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('productable.url')
                    ->label('URL')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('purpose')
                    ->label('Propósito')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'primary' => 'primary',
                        'included' => 'success',
                        'bonus' => 'warning',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'primary' => 'Principal',
                        'included' => 'Incluido',
                        'bonus' => 'Bonus',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('access_policy')
                    ->label('Política de Acceso')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'immediate' => 'success',
                        'after_payment' => 'warning',
                        'scheduled' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Vinculado el')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('purpose')
                    ->label('Propósito')
                    ->options([
                        'primary' => 'Principal',
                        'included' => 'Incluido',
                        'bonus' => 'Bonus',
                    ]),

                Tables\Filters\SelectFilter::make('access_policy')
                    ->label('Política de Acceso')
                    ->options([
                        'immediate' => 'Inmediato',
                        'after_payment' => 'Después del Pago',
                        'scheduled' => 'Programado',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['productable_type'] = File::class;
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
