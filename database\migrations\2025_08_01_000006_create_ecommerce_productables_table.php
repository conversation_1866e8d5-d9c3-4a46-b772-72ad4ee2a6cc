<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('lbcdev_ecommerce_productables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('lbcdev_ecommerce_products')->onDelete('cascade');

            // Relación polimórfica con cualquier modelo linkable
            $table->string('productable_type');
            $table->unsignedBigInteger('productable_id');

            // Metadatos para la relación
            $table->string('purpose')->default('primary'); // primary, included, bonus, grants_access, etc.
            $table->string('access_policy')->default('immediate'); // immediate, after_payment, scheduled, etc.
            $table->json('meta')->nullable(); // configuración adicional (duración, licencias, etc.)

            $table->timestamps();

            // Índices con nombres cortos
            $table->index(['productable_type', 'productable_id'], 'productables_morph_idx');
            $table->index('product_id', 'productables_product_idx');

            // Prevenir duplicados
            $table->unique(['product_id', 'productable_type', 'productable_id'], 'productables_unique_idx');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('lbcdev_ecommerce_productables');
    }
};
