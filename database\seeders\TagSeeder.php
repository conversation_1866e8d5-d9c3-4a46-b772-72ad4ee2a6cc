<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use LBCDev\Ecommerce\Models\Tag;

class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $type = config('lbcdev-ecommerce.tags_taxonomy_type');

        $tags = [
            ['name' => 'Oferta', 'slug' => 'oferta', 'description' => 'En oferta'],
            ['name' => 'Nuevo', 'slug' => 'nuevo', 'description' => 'Nuevo'],
            ['name' => 'Destacado', 'slug' => 'destacado', 'description' => 'Destacado'],
        ];

        foreach ($tags as $tag) {
            Tag::firstOrCreate([
                'slug' => $tag['slug'],
                'type' => $type,
            ], [
                'name' => $tag['name'],
                'description' => $tag['description'],
            ]);
        }
    }
}
