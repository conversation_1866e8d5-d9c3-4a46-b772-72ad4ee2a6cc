<div class="space-y-4" x-data>
    <div class="flex flex-wrap gap-2 mb-4">
        @foreach($blockViews as $type => $view)
            <button wire:click="addBlock('{{ $type }}')"
                class="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                + {{ ucfirst($type) }}
            </button>
        @endforeach
    </div>

    @foreach($blocks as $index => $block)
        <div class="p-4 border rounded shadow-sm bg-white">
            <div class="flex justify-between items-center mb-2">
                <strong>{{ ucfirst($block['type']) }}</strong>

                <div class="space-x-1">
                    <button wire:click="moveBlockUp({{ $index }})" class="text-xs text-gray-600 hover:text-black">↑</button>
                    <button wire:click="moveBlockDown({{ $index }})" class="text-xs text-gray-600 hover:text-black">↓</button>
                    <button wire:click="removeBlock('{{ $block['uuid'] }}')" class="text-xs text-red-600">✕</button>
                </div>
            </div>

            @includeIf($blockViews[$block['type']] ?? '', [
                'data' => &$blocks[$index]['data'],
            ])
        </div>
    @endforeach

    <div class="mt-6">
        <button wire:click="save" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
            Guardar página
        </button>
    </div>

    @if (session()->has('saved'))
        <div class="text-green-600 mt-2">{{ session('saved') }}</div>
    @endif
</div>
