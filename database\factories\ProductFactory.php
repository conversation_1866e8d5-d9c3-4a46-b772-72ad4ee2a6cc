<?php

// database/factories/ProductFactory.php
namespace LBCDev\Ecommerce\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use LBCDev\Ecommerce\Models\Product;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        return [
            'name'        => $this->faker->word(),
            'description' => $this->faker->sentence(),
            'price'       => $this->faker->randomFloat(2, 5, 100),
            'tax_rate'    => 21.0,
            'is_active'   => true,
        ];
    }
}
