<?php

namespace App\Filament\Resources\OAuthServiceResource\Pages;

use App\Filament\Resources\OAuthServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateOAuthService extends CreateRecord
{
    protected static string $resource = OAuthServiceResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // comprobar si $data['credentials'] es vacio y si lo es llenar con los valores de la configuración
        if (empty($data['credentials'])) {
            $data['credentials'] = config('oauth-manager.services.' . $data['service_type'] . '.fields');
        }

        return static::getModel()::create($data);
    }
}
