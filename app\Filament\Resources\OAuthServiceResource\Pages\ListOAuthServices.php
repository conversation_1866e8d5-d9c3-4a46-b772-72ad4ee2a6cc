<?php

namespace App\Filament\Resources\OAuthServiceResource\Pages;

use App\Filament\Resources\OAuthServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOAuthServices extends ListRecords
{
    protected static string $resource = OAuthServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
