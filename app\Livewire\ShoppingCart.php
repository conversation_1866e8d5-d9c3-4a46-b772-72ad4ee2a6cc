<?php

namespace App\Livewire;

use App\Models\Product;
use Illuminate\Support\Facades\Session;
use Livewire\Component;

class ShoppingCart extends Component
{
    public $cart = [];
    public $total = 0;
    public $count = 0;
    public $showCart = false;

    protected $listeners = ['cartUpdated' => 'loadCart'];

    public function mount()
    {
        $this->loadCart();
    }

    public function loadCart()
    {
        $this->cart = Session::get('cart', []);
        $this->calculateTotals();
    }

    public function addToCart($productId, $quantity = 1)
    {
        $product = Product::find($productId);

        if (!$product || !$product->activo) {
            session()->flash('error', 'Producto no disponible.');
            return;
        }

        $cart = Session::get('cart', []);

        if (isset($cart[$productId])) {
            $cart[$productId]['quantity'] += $quantity;
        } else {
            $cart[$productId] = [
                'id' => $product->id,
                'nombre' => $product->nombre,
                'precio' => $product->precio,
                'quantity' => $quantity,
                'slug' => $product->slug,
            ];
        }

        // Limit quantity per product
        if ($cart[$productId]['quantity'] > 10) {
            $cart[$productId]['quantity'] = 10;
        }

        Session::put('cart', $cart);
        $this->loadCart();

        session()->flash('success', 'Producto agregado al carrito.');
        $this->dispatch('cartUpdated');
    }

    public function updateQuantity($productId, $quantity)
    {
        $cart = Session::get('cart', []);

        if (!isset($cart[$productId])) {
            return;
        }

        if ($quantity <= 0) {
            $this->removeFromCart($productId);
            return;
        }

        if ($quantity > 10) {
            $quantity = 10;
        }

        $cart[$productId]['quantity'] = $quantity;
        Session::put('cart', $cart);
        $this->loadCart();

        $this->dispatch('cartUpdated');
    }

    public function removeFromCart($productId)
    {
        $cart = Session::get('cart', []);

        if (isset($cart[$productId])) {
            unset($cart[$productId]);
            Session::put('cart', $cart);
            $this->loadCart();

            session()->flash('success', 'Producto eliminado del carrito.');
            $this->dispatch('cartUpdated');
        }
    }

    public function clearCart()
    {
        Session::forget('cart');
        $this->loadCart();

        session()->flash('success', 'Carrito vaciado.');
        $this->dispatch('cartUpdated');
    }

    public function toggleCart()
    {
        $this->showCart = !$this->showCart;
    }

    private function calculateTotals()
    {
        $this->total = 0;
        $this->count = 0;

        foreach ($this->cart as $item) {
            $this->total += $item['precio'] * $item['quantity'];
            $this->count += $item['quantity'];
        }

        $this->total = round($this->total, 2);
    }

    public function render()
    {
        return view('livewire.shopping-cart');
    }
}
