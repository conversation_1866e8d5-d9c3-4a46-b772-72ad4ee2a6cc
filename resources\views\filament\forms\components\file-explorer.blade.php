@php
    $oauthServiceField = $getOauthServiceField();
    $oauthServiceId = null;
    $serviceType = null;
    
    if ($oauthServiceField) {
        $oauthServiceId = data_get($getRecord(), $oauthServiceField) ?? $this->form->getRawState()[$oauthServiceField] ?? null;
        
        // Get service type if we have an OAuth service ID
        if ($oauthServiceId) {
            $oauthService = \LBCDev\OAuthManager\Models\OAuthService::find($oauthServiceId);
            $serviceType = $oauthService?->service_type;
        }
    }
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div class="space-y-4"
         x-data="{
             fieldValue: @entangle($getStatePath()).live
         }"
         x-init="
             window.addEventListener('fileSelected-{{ str_replace('.', '-', $getStatePath()) }}', (event) => {
                 fieldValue = event.detail.fileUrl;
             });
         ">
        {{-- Hidden input to store the selected file URL --}}
        <input
            type="hidden"
            {!! $applyStateBindingModifiers('wire:model') !!}="{{ $getStatePath() }}"
            x-model="fieldValue"
        />

        {{-- Current Value Display --}}
        @if($getState())
            <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Current file:</p>
                            <p class="text-xs text-gray-600 break-all">{{ $getState() }}</p>
                        </div>
                    </div>
                    <button
                        type="button"
                        x-on:click="fieldValue = ''"
                        class="text-red-600 hover:text-red-800 text-sm"
                    >
                        Clear
                    </button>
                </div>
            </div>
        @endif

        {{-- Service Type Display --}}
        @if($serviceType)
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
                </svg>
                Service: {{ $getServiceTypeDisplayName($serviceType) }}
            </div>
        @endif

        {{-- OAuth File Explorer Component --}}
        @if($oauthServiceId)
            @livewire('oauth-file-explorer', [
                'oauthServiceId' => $oauthServiceId,
                'fieldName' => $getStatePath(),
                'acceptedMimeTypes' => $getAcceptedMimeTypes()
            ], key($getStatePath() . '-' . $oauthServiceId))
        @else
            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-yellow-800">
                            Service required
                        </p>
                        <p class="text-xs text-yellow-700 mt-1">
                            Please select an active OAuth service first to browse files.
                            @if($oauthServiceField)
                                Make sure the "{{ $oauthServiceField }}" field has a valid service selected.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        @endif

        {{-- Instructions --}}
        @if(!$getState() && $oauthServiceId)
            <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-blue-800">How to use:</p>
                        <ul class="text-xs text-blue-700 mt-1 space-y-1">
                            <li>• Browse folders by clicking on them</li>
                            <li>• Click on a file to select it</li>
                            <li>• Use the search bar to find specific files</li>
                            <li>• Use breadcrumbs to navigate back to parent folders</li>
                            @if(!empty($getAcceptedMimeTypes()))
                                <li>• Only certain file types are accepted for this field</li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        {{-- Accepted File Types Info --}}
        @if(!empty($getAcceptedMimeTypes()))
            <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <p class="text-xs font-medium text-gray-700 mb-1">Accepted file types:</p>
                <div class="flex flex-wrap gap-1">
                    @foreach($getAcceptedMimeTypes() as $mimeType)
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-200 text-gray-700">
                            {{ $mimeType }}
                        </span>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</x-dynamic-component>
