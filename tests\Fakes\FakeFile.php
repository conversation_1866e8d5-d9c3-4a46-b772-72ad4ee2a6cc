<?php

namespace Tests\Fakes;

/**
 * Modelo falso que simula un archivo para tests de linkables.
 */
class FakeFile
{
    public $id;
    public $name;
    public $path;

    public function __construct($id = 1, $name = 'test-file.pdf', $path = '/uploads/test-file.pdf')
    {
        $this->id = $id;
        $this->name = $name;
        $this->path = $path;
    }

    public function getKey()
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getPath(): string
    {
        return $this->path;
    }
}
