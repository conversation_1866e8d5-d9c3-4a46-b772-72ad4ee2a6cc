<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\User;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Aliziodev\LaravelTaxonomy\Enums\TaxonomyType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ProductCrudTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $category;
    protected $tag;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create();

        // Create test taxonomies
        $this->category = Taxonomy::create([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'type' => TaxonomyType::Category->value,
            'description' => 'Electronic products',
        ]);

        $this->tag = Taxonomy::create([
            'name' => 'Featured',
            'slug' => 'featured',
            'type' => TaxonomyType::Tag->value,
            'description' => 'Featured products',
        ]);
    }

    public function test_can_create_product_with_taxonomies()
    {
        $productData = [
            'nombre' => 'iPhone 15 Pro',
            'slug' => 'iphone-15-pro',
            'descripcion_corta' => 'Latest iPhone model',
            'descripcion_larga' => 'The most advanced iPhone with cutting-edge technology',
            'precio' => 999.99,
            'precio_descuento' => 899.99,
            'activo' => true,
            'orden' => 1,
            'categories' => [$this->category->id],
            'tags' => [$this->tag->id],
        ];

        $product = Product::create($productData);

        // Attach taxonomies after creation
        if (isset($productData['categories'])) {
            $product->attachTaxonomies($productData['categories']);
        }
        if (isset($productData['tags'])) {
            $product->attachTaxonomies($productData['tags']);
        }

        $this->assertDatabaseHas('products', [
            'nombre' => 'iPhone 15 Pro',
            'slug' => 'iphone-15-pro',
            'precio' => 999.99,
        ]);

        $this->assertTrue($product->hasTaxonomies([$this->category]));
        $this->assertTrue($product->hasTaxonomies([$this->tag]));
    }

    public function test_can_update_product_with_taxonomies()
    {
        $product = Product::factory()->create([
            'nombre' => 'Old Product Name',
            'precio' => 100.00,
        ]);

        $product->attachTaxonomies([$this->category]);

        $updateData = [
            'nombre' => 'Updated Product Name',
            'precio' => 150.00,
            'categories' => [$this->category->id],
            'tags' => [$this->tag->id],
        ];

        $product->update($updateData);

        // Sync taxonomies
        $product->syncTaxonomies(array_merge(
            $updateData['categories'] ?? [],
            $updateData['tags'] ?? []
        ));

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'nombre' => 'Updated Product Name',
            'precio' => 150.00,
        ]);

        $this->assertTrue($product->fresh()->hasTaxonomies([$this->category]));
        $this->assertTrue($product->fresh()->hasTaxonomies([$this->tag]));
    }

    public function test_can_delete_product()
    {
        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->category, $this->tag]);

        $productId = $product->id;

        $product->delete();

        $this->assertDatabaseMissing('products', [
            'id' => $productId,
        ]);

        // Check that taxonomy relationships are also deleted
        $this->assertDatabaseMissing('taxonomables', [
            'taxonomable_id' => $productId,
            'taxonomable_type' => Product::class,
        ]);
    }

    public function test_product_slug_must_be_unique()
    {
        Product::factory()->create(['slug' => 'unique-slug']);

        $this->expectException(\Illuminate\Database\QueryException::class);

        Product::factory()->create(['slug' => 'unique-slug']);
    }

    public function test_product_can_have_multiple_categories()
    {
        $category2 = Taxonomy::create([
            'name' => 'Smartphones',
            'slug' => 'smartphones',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $this->category->id,
        ]);

        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->category, $category2]);

        $categories = $product->taxonomiesOfType(TaxonomyType::Category);

        $this->assertCount(2, $categories);
        $this->assertTrue($categories->contains($this->category));
        $this->assertTrue($categories->contains($category2));
    }

    public function test_product_can_have_multiple_tags()
    {
        $tag2 = Taxonomy::create([
            'name' => 'New',
            'slug' => 'new',
            'type' => TaxonomyType::Tag->value,
        ]);

        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->tag, $tag2]);

        $tags = $product->taxonomiesOfType(TaxonomyType::Tag);

        $this->assertCount(2, $tags);
        $this->assertTrue($tags->contains($this->tag));
        $this->assertTrue($tags->contains($tag2));
    }

    public function test_can_filter_products_by_price_range()
    {
        $product1 = Product::factory()->create(['precio' => 100.00]);
        $product2 = Product::factory()->create(['precio' => 200.00]);
        $product3 = Product::factory()->create(['precio' => 300.00]);

        $productsInRange = Product::whereBetween('precio', [150, 250])->get();

        $this->assertCount(1, $productsInRange);
        $this->assertEquals($product2->id, $productsInRange->first()->id);
    }

    public function test_can_filter_active_products()
    {
        $activeProduct = Product::factory()->create(['activo' => true]);
        $inactiveProduct = Product::factory()->create(['activo' => false]);

        $activeProducts = Product::where('activo', true)->get();

        $this->assertCount(1, $activeProducts);
        $this->assertEquals($activeProduct->id, $activeProducts->first()->id);
    }

    public function test_products_are_ordered_by_orden_field()
    {
        $product1 = Product::factory()->create(['orden' => 3]);
        $product2 = Product::factory()->create(['orden' => 1]);
        $product3 = Product::factory()->create(['orden' => 2]);

        $orderedProducts = Product::orderBy('orden')->get();

        $this->assertEquals($product2->id, $orderedProducts->first()->id);
        $this->assertEquals($product3->id, $orderedProducts->get(1)->id);
        $this->assertEquals($product1->id, $orderedProducts->last()->id);
    }

    public function test_product_factory_creates_valid_products()
    {
        $product = Product::factory()->create();

        $this->assertNotNull($product->nombre);
        $this->assertNotNull($product->slug);
        $this->assertIsNumeric($product->precio);
        $this->assertIsBool($product->activo);
        $this->assertIsInt($product->orden);
    }
}
