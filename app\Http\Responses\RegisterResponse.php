<?php

namespace App\Http\Responses;

use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;
use Filament\Http\Responses\Auth\RegistrationResponse;

class RegisterResponse extends RegistrationResponse
{
    public function toResponse($request): RedirectResponse|Redirector
    {
        /** @var User */
        $user = Auth::user();

        // Si no hay usuario autenticado o no tiene rol, redirigir al home
        if (!$user || !$user->hasRole('admin') && !$user->hasRole('cliente')) {
            return redirect()->route('home');
        }

        $redirectUrl = '/';

        // Determinar URL de redirección según el rol
        if ($user->hasRole('admin')) {
            $redirectUrl = '/admin';
        } elseif ($user->hasRole('cliente')) {
            $redirectUrl = '/app';
        }

        // Crear una vista temporal para redirección with JavaScript (igual que el login)
        return redirect($redirectUrl);
        // return response()->view('dashboard-redirect', compact('redirectUrl'));
    }
}
