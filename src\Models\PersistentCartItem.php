<?php

namespace LBCDev\Ecommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class PersistentCartItem extends Model
{
    protected $table = 'lbcdev_ecommerce_cart_items';

    protected $fillable = [
        'cart_id',
        'sellable_type',
        'sellable_id',
        'name',
        'quantity',
        'unit_price',
        'tax_rate',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'float',
        'tax_rate' => 'float',
    ];

    /**
     * Relación con el carrito persistente.
     */
    public function cart(): BelongsTo
    {
        return $this->belongsTo(PersistentCart::class, 'cart_id');
    }

    /**
     * Relación polimórfica con el item vendible.
     */
    public function sellable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Verificar si el item original aún existe.
     */
    public function hasOriginalItem(): bool
    {
        return $this->sellable !== null;
    }

    /**
     * Obtener subtotal sin impuestos.
     */
    public function getSubtotal(): float
    {
        return $this->unit_price * $this->quantity;
    }

    /**
     * Obtener subtotal con impuestos.
     */
    public function getSubtotalWithTax(): float
    {
        return $this->getSubtotal() * (1 + $this->tax_rate / 100);
    }
}
