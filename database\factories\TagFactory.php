<?php

namespace LBCDev\Ecommerce\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use LBCDev\Ecommerce\Models\Tag;
use Illuminate\Support\Str;

class TagFactory extends Factory
{
    protected $model = Tag::class;

    public function definition(): array
    {
        $name = $this->faker->unique()->words(2, true);

        return [
            'name'        => ucfirst($name),
            'slug'        => Str::slug($name),
            'description' => $this->faker->sentence(),
            'type'        => config('lbcdev-ecommerce.tags_taxonomy_type'),
        ];
    }
}
