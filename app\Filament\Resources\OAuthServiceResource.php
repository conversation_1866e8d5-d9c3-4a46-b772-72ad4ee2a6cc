<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OAuthServiceResource\Pages;
use App\Filament\Resources\OAuthServiceResource\RelationManagers;
use LBCDev\OAuthManager\Models\OAuthService;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OAuthServiceResource extends Resource
{
    protected static ?string $model = OAuthService::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';
    protected static ?string $navigationLabel = 'OAuth Services';
    protected static ?string $navigationGroup = 'Configuración';
    protected static ?string $modelLabel = 'OAuth Service';
    protected static ?string $pluralModelLabel = 'OAuth Services';


    protected static null|int $navigationSort = 11;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Service Configuration')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Select::make('service_type')
                            ->required()
                            ->options(
                                collect(config('oauth-manager.services'))
                                    ->mapWithKeys(fn($config, $key) => [$key => $config['name']])
                                    ->toArray()
                            )
                            ->live()
                            ->afterStateUpdated(fn($state, Forms\Set $set) => $set('credentials', [])),

                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(OAuthService::class, 'slug', fn($record) => $record),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('OAuth Status')
                    ->schema([
                        Forms\Components\Placeholder::make('access_token_status')
                            ->label('Access Token')
                            ->content(function (OAuthService $record) {
                                if (!$record?->access_token) {
                                    return 'Not authorized';
                                }

                                if ($record->isTokenExpired()) {
                                    return 'Expired';
                                }

                                return 'Valid';
                            }),

                        Forms\Components\Placeholder::make('last_used_at')
                            ->label('Last Used')
                            ->content(fn(OAuthService $record) => $record?->last_used_at?->diffForHumans() ?? 'Never'),
                        Placeholder::make('expires_at')
                            ->label('Expires')
                            ->content(fn(OAuthService $record) => $record?->expires_at?->diffForHumans() ?? 'Never'),
                    ])
                    ->columns(3)
                    ->visible(fn(string $operation) => $operation === 'edit'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('service_type')
                    ->label('Service')
                    ->formatStateUsing(fn($state) => config("oauth-manager.services.{$state}.name", $state))
                    ->badge()
                    ->color('primary'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('oauth_status')
                    ->label('OAuth Status')
                    ->getStateUsing(function (OAuthService $record) {
                        if (!$record->access_token) {
                            return 'not_authorized';
                        }

                        if ($record->isTokenExpired()) {
                            return 'expired';
                        }

                        return 'authorized';
                    })
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'authorized' => 'success',
                        'expired' => 'warning',
                        'not_authorized' => 'danger',
                    })
                    ->formatStateUsing(fn($state) => match ($state) {
                        'authorized' => 'Authorized',
                        'expired' => 'Expired',
                        'not_authorized' => 'Not Authorized',
                    }),

                Tables\Columns\TextColumn::make('last_used_at')
                    ->label('Last Used')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('service_type')
                    ->label('Service Type')
                    ->options(
                        collect(config('oauth-manager.services'))
                            ->mapWithKeys(fn($config, $key) => [$key => $config['name']])
                            ->toArray()
                    ),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),
            ])
            ->actions([
                Tables\Actions\Action::make('authorize')
                    ->label('Authorize')
                    ->icon('heroicon-m-key')
                    ->color('success')
                    ->visible(fn(OAuthService $record) => !$record->access_token || $record->isTokenExpired())
                    ->url(fn(OAuthService $record) => route('oauth-manager.authorize', $record)),

                Tables\Actions\Action::make('revoke')
                    ->label('Revoke')
                    ->icon('heroicon-m-x-circle')
                    ->color('danger')
                    ->visible(fn(OAuthService $record) => $record->access_token && !$record->isTokenExpired())
                    ->requiresConfirmation()
                    ->modalHeading('Revoke OAuth Token')
                    ->modalDescription('Are you sure you want to revoke this OAuth token? This will disconnect the service.')
                    ->modalSubmitActionLabel('Revoke Token')
                    ->action(function (OAuthService $record) {
                        $success = $record->revokeToken();

                        if ($success) {
                            \Filament\Notifications\Notification::make()
                                ->title('Token revoked successfully')
                                ->success()
                                ->send();
                        } else {
                            \Filament\Notifications\Notification::make()
                                ->title('Token cleared locally')
                                ->body('Remote revocation may have failed, but local tokens were cleared.')
                                ->warning()
                                ->send();
                        }
                    }),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('revoke_tokens')
                        ->label('Revoke Tokens')
                        ->icon('heroicon-m-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('Revoke OAuth Tokens')
                        ->modalDescription('Are you sure you want to revoke the selected OAuth tokens? This will disconnect the services.')
                        ->modalSubmitActionLabel('Revoke Tokens')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                            $revokedCount = 0;
                            $failedCount = 0;

                            foreach ($records as $record) {
                                if ($record->access_token) {
                                    if ($record->revokeToken()) {
                                        $revokedCount++;
                                    } else {
                                        $failedCount++;
                                    }
                                }
                            }

                            if ($revokedCount > 0) {
                                \Filament\Notifications\Notification::make()
                                    ->title("Successfully revoked {$revokedCount} token(s)")
                                    ->success()
                                    ->send();
                            }

                            if ($failedCount > 0) {
                                \Filament\Notifications\Notification::make()
                                    ->title("Failed to revoke {$failedCount} token(s)")
                                    ->body('Tokens were cleared locally, but remote revocation may have failed.')
                                    ->warning()
                                    ->send();
                            }
                        }),

                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOAuthServices::route('/'),
            'create' => Pages\CreateOAuthService::route('/create'),
            'edit' => Pages\EditOAuthService::route('/{record}/edit'),
        ];
    }
}
