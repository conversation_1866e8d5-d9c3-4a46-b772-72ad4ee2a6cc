<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use LBCDev\Ecommerce\Models\Product;

class Course extends Model
{
    /** @use HasFactory<\Database\Factories\CourseFactory> */
    use HasFactory;

    protected $fillable = [
        'titulo',
        'descripcion',
        'slug',
        'publicado',
    ];

    public function products()
    {
        return $this->morphToMany(
            Product::class,
            'productable',
            'lbcdev_ecommerce_productables',
            relatedPivotKey: 'product_id',
            foreignPivotKey: 'productable_id'
        )->withPivot(['purpose', 'access_policy', 'meta'])
            ->withTimestamps();
    }

    public function lessons()
    {
        return $this->hasMany(Lesson::class);
    }
}
