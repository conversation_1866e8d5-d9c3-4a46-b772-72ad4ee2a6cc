<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Page;

class CmsSeeder extends Seeder
{
    public function run(): void
    {
        // Crear menú principal
        $menu = Menu::firstOrCreate(['name' => 'principal']);

        // Crear elementos del menú
        $menuItems = [
            ['label' => 'Inicio', 'url' => '/', 'order' => 0],
            ['label' => 'Cursos', 'url' => '/cursos', 'order' => 1],
            ['label' => 'Pedir Cita', 'url' => '/pedir-cita', 'order' => 2],
            ['label' => 'Sobre Mí', 'url' => '/sobre-mi', 'order' => 3],
        ];

        foreach ($menuItems as $item) {
            MenuItem::firstOrCreate([
                'menu_id' => $menu->id,
                'label' => $item['label'],
                'url' => $item['url'],
                'order' => $item['order'],
            ]);
        }

        // Crear páginas
        $pages = [
            [
                'title' => 'Pedir Cita',
                'slug' => 'pedir-cita',
                'content' => '<p>Aquí puedes reservar una cita conmigo. Usa el formulario.</p>',
                'is_published' => true,
            ],
            [
                'title' => 'Sobre Mí',
                'slug' => 'sobre-mi',
                'content' => '<p>Soy un terapeuta con años de experiencia en ayudar a personas a lograr el equilibrio emocional.</p>',
                'is_published' => true,
            ],
        ];

        foreach ($pages as $data) {
            Page::firstOrCreate(
                ['slug' => $data['slug']],
                $data
            );
        }
    }
}
