<?php

namespace LBCDev\Ecommerce\Traits;

use Illuminate\Support\Collection;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomy;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasCategories
{
    use HasTaxonomy;

    /**
     * Tipo de taxonomía para categorías (puede sobreescribirse si hace falta).
     */
    public function getCategoryTaxonomyType(): string
    {
        return config('lbcdev-ecommerce.categories_taxonomy_type', 'lbcdev-ecommerce-category');
    }

    /**
     * Devuelve las categorías asociadas al modelo.
     */
    public function categories(): Collection
    {
        return $this->taxonomies()
            ->where('type', $this->getCategoryTaxonomyType())
            ->get();
    }

    /**
     * Asocia una o varias categorías al modelo.
     */
    public function addCategory($categories): self
    {
        return $this->attachTaxonomies($categories);
    }

    /**
     * Elimina una o varias categorías del modelo.
     */
    public function removeCategory($categories = null): self
    {
        return $this->detachTaxonomies($categories);
    }

    /**
     * Reemplaza todas las categorías por las nuevas.
     */
    public function syncCategories($categories): self
    {
        $this->categoriesRelation()->each(function ($category) {
            $this->categoriesRelation()->detach($category);
        });

        // Detectar si $categories es Collection o array para recorrer
        if ($categories instanceof Collection) {
            $categories = $categories->all();
        }
        if (!is_array($categories)) {
            $categories = [$categories];
        }

        foreach ($categories as $category) {
            $this->categoriesRelation()->attach($category);
        }

        return $this;
    }

    /**
     * Verifica si el modelo tiene una categoría o varias.
     */
    public function hasCategory($categories): bool
    {
        return $this->hasTaxonomies($categories);
    }

    /**
     * Relacion polimórfica con las categorías.
     */
    public function categoriesRelation(): MorphToMany
    {
        return $this->morphToMany(
            Taxonomy::class,
            'taxonomable',
            'taxonomables', // Nombre explícito de tabla pivot
            'taxonomable_id', // Columna para el ID del modelo
            'taxonomy_id'    // Columna para el ID de taxonomía
        )->where('type', $this->getCategoryTaxonomyType());
    }
}
