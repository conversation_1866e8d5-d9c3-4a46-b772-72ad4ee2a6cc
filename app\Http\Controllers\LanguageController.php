<?php

namespace App\Http\Controllers;

use App\Models\Language;
use Illuminate\Http\Request;
use <PERSON>cam<PERSON>\LaravelLocalization\Facades\LaravelLocalization;

class LanguageController extends Controller
{
    public function set(Request $request)
    {
        $locale = $request->input('locale');

        if (in_array($locale, array_keys(LaravelLocalization::getSupportedLocales()))) {
            session(['locale' => $locale]);
            app()->setLocale($locale);
        }

        $redirectUrl = LaravelLocalization::getLocalizedURL($locale, $request->input('redirect', '/'));

        return redirect($redirectUrl);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Language $language)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Language $language)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Language $language)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Language $language)
    {
        //
    }
}
