<?php

namespace LBCDev\Ecommerce\Models;

use Illuminate\Foundation\Auth\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PersistentCart extends Model
{
    protected $table = 'lbcdev_ecommerce_carts';

    protected $fillable = [
        'user_id',
        'session_id',
        'identifier',
        'total',
        'total_with_tax',
        'expires_at',
    ];

    protected $casts = [
        'total' => 'float',
        'total_with_tax' => 'float',
        'expires_at' => 'datetime',
    ];

    /**
     * Relación con los items del carrito.
     */
    public function items(): HasMany
    {
        return $this->hasMany(PersistentCartItem::class, 'cart_id');
    }

    /**
     * Relación con el usuario.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(config('auth.providers.users.model'));
    }

    /**
     * Convertir a Cart en memoria.
     */
    public function toMemoryCart(): Cart
    {
        $cart = new Cart();

        foreach ($this->items as $persistentItem) {
            if ($persistentItem->sellable) {
                $cart->addItem($persistentItem->sellable, $persistentItem->quantity);
            }
        }

        return $cart;
    }

    /**
     * Actualizar desde Cart en memoria.
     */
    public function updateFromMemoryCart(Cart $memoryCart): void
    {
        // Limpiar items existentes
        $this->items()->delete();

        // Agregar nuevos items
        foreach ($memoryCart->getItems() as $cartItem) {
            $this->items()->create([
                'sellable_type' => get_class($cartItem->getItem()),
                'sellable_id' => $cartItem->getId(),
                'name' => $cartItem->getName(),
                'quantity' => $cartItem->getQuantity(),
                'unit_price' => $cartItem->getUnitPrice(),
                'tax_rate' => $cartItem->getItem()->getSellableTaxRate(),
            ]);
        }

        // Actualizar totales
        $this->update([
            'total' => $memoryCart->getTotal(),
            'total_with_tax' => $memoryCart->getTotalWithTax(),
        ]);
    }

    /**
     * Scope para carritos expirados.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Scope para carritos activos.
     */
    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now());
    }
}
