<?php

namespace Tests\Unit;

use LBCDev\Ecommerce\Models\Cart;
use LBCDev\Ecommerce\Models\CartItem;
use PHPUnit\Framework\TestCase;
use Tests\Fakes\FakeSellable;

class CartTest extends TestCase
{
    public function testAddItemAndGetItems()
    {
        $cart = new Cart();
        $item1 = new FakeSellable();

        $cart->addItem($item1, 2);

        $items = $cart->getItems();

        $this->assertCount(1, $items);
        $this->assertInstanceOf(CartItem::class, $items[0]);
        $this->assertEquals(2, $items[0]->getQuantity());
        $this->assertEquals($item1, $items[0]->getItem());
    }

    public function testAddSameItemIncrementsQuantity()
    {
        $cart = new Cart();
        $item = new FakeSellable();

        $cart->addItem($item, 1);
        $cart->addItem($item, 3);

        $items = $cart->getItems();

        $this->assertCount(1, $items);
        $this->assertEquals(4, $items[0]->getQuantity());
    }

    public function testAddDifferentItems()
    {
        $cart = new Cart();
        $item1 = new FakeSellable(id: 1);
        $item2 = new FakeSellable(id: 2);

        $cart->addItem($item1, 1);
        $cart->addItem($item2, 1);

        $items = $cart->getItems();

        $this->assertCount(2, $items);
    }

    public function testRemoveItem()
    {
        $cart = new Cart();
        $item = new FakeSellable();

        $cart->addItem($item, 1);
        $cart->removeItem($item);

        $this->assertCount(0, $cart->getItems());
    }

    public function testUpdateQuantity()
    {
        $cart = new Cart();
        $item = new FakeSellable();

        $cart->addItem($item, 1);
        $cart->updateQuantity($item, 5);

        $items = $cart->getItems();
        $this->assertEquals(5, $items[0]->getQuantity());

        // Quantity less than 1 removes the item
        $cart->updateQuantity($item, 0);
        $this->assertCount(0, $cart->getItems());
    }

    public function testGetTotalAndTotalWithTax()
    {
        $cart = new Cart();
        $item1 = new FakeSellable(id: 1, price: 50, tax_rate: 10);
        $item2 = new FakeSellable(id: 2, price: 50, tax_rate: 10);

        $cart->addItem($item1, 2); // price 50 * 2 = 100
        $cart->addItem($item2, 3); // price 50 * 3 = 150

        $expectedTotal = 100 + 150; // 250
        $expectedTotalWithTax = (50 * 1.10) * 2 + (50 * 1.10) * 3; // 110 + 165 = 275

        $this->assertEquals($expectedTotal, $cart->getTotal());
        $this->assertEqualsWithDelta($expectedTotalWithTax, $cart->getTotalWithTax(), 0.01);
    }

    public function testClearCart()
    {
        $cart = new Cart();
        $item = new FakeSellable();

        $cart->addItem($item, 1);
        $this->assertCount(1, $cart->getItems());

        $cart->clear();
        $this->assertCount(0, $cart->getItems());
    }
}
