# CLAUDE.md - Reglas para Desarrollo Asistido con IA

## 🎯 Filosofía de Desarrollo

Este proyecto sigue los principios de **<PERSON><PERSON>ost** para desarrollo asistido con IA, priorizando:

- **Código limpio y mantenible** siguiendo las convenciones de Laravel
- **Testing exhaustivo** con PHPUnit y cobertura completa
- **Documentación clara** en español para el equipo
- **Arquitectura desacoplada** con patrones SOLID
- **Desarrollo iterativo** con feedback continuo

---

## 📋 Reglas Generales para IA

### 1. **Análisis Previo Obligatorio**
- **SIEMPRE** usar `codebase-retrieval` antes de hacer cambios
- Examinar la estructura existente y patrones establecidos
- Verificar dependencias y relaciones entre componentes
- Revisar tests existentes para entender el comportamiento esperado

### 2. **Convenciones de Código**
- Seguir **PSR-12** y estándares de Laravel
- Usar **nombres descriptivos** en español para métodos de test
- Mantener **consistencia** con el namespace `LBCDev\Ecommerce`
- Aplicar **principios SOLID** en el diseño de clases

### 3. **Gestión de Dependencias**
- **NUNCA** editar manualmente `composer.json`
- Usar comandos de Composer: `composer require`, `composer remove`
- Verificar compatibilidad con Laravel 10+, 11+, 12+
- Mantener PHP ^8.1 como requisito mínimo

---

## 🧪 Reglas de Testing

### Estructura de Tests
```
tests/
├── Unit/           # Tests unitarios aislados
├── Feature/        # Tests de integración
└── Fakes/          # Objetos mock para testing
```

### Convenciones de Naming
- **Archivos**: `{Clase}Test.php` (ej: `ProductTest.php`)
- **Métodos**: `test_{descripcion_en_español}()` 
- **Ejemplo**: `test_el_sistema_puede_crear_un_producto()`

### Configuración de Test
- Usar **Orchestra Testbench** para tests de paquetes
- Base de datos **SQLite en memoria** para velocidad
- **RefreshDatabase** trait para aislamiento
- Configurar providers en `getPackageProviders()`

### Estructura de Test Unitario
```php
<?php

namespace LBCDev\Ecommerce\Tests\Unit;

use Orchestra\Testbench\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ExampleTest extends TestCase
{
    use RefreshDatabase;

    protected function getPackageProviders($app)
    {
        return [
            \LBCDev\Ecommerce\EcommerceServiceProvider::class,
        ];
    }

    protected function getEnvironmentSetUp($app)
    {
        $app['config']->set('database.default', 'sqlite');
        $app['config']->set('database.connections.sqlite', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);
    }

    public function test_descripcion_clara_del_comportamiento()
    {
        // Arrange - Preparar datos
        
        // Act - Ejecutar acción
        
        // Assert - Verificar resultado
    }
}
```

### Comandos de Testing
```bash
# Tests unitarios con descripción
vendor/bin/phpunit --testdox --testsuite=Unit

# Tests con cobertura
$env:XDEBUG_MODE = "coverage"
vendor/bin/phpunit --coverage-html=build/coverage --testdox --testsuite=Unit

# Test específico
vendor/bin/phpunit tests/Unit/ProductTest.php
```

---

## 🏗️ Arquitectura del Paquete

### Estructura de Directorios
```
src/
├── EcommerceServiceProvider.php    # Service Provider principal
├── Providers/                      # Service Providers adicionales
├── Models/                         # Modelos Eloquent
├── Services/                       # Lógica de negocio
├── Events/                         # Eventos del sistema
├── Console/Commands/               # Comandos Artisan
└── Contracts/                      # Interfaces y contratos
```

### Patrones Establecidos
- **Service Provider Pattern** para registro de servicios
- **Repository Pattern** para acceso a datos
- **Event-Driven Architecture** para desacoplamiento
- **Factory Pattern** para creación de objetos complejos

---

## 🔧 Configuración y Servicios

### Archivo de Configuración
- Ubicación: `config/ecommerce.php`
- Namespace de config: `lbcdev-ecommerce`
- Usar `env()` para valores configurables
- Documentar cada opción con comentarios

### Service Providers
- Registrar en `EcommerceServiceProvider`
- Usar `mergeConfigFrom()` para configuración
- Publicar assets con tags específicos
- Configurar auto-discovery en `composer.json`

---

## 📝 Documentación

### README.md
- Mantener secciones actualizadas
- Incluir ejemplos de uso
- Documentar comandos de instalación
- Explicar configuración paso a paso

### API_REFERENCE.md
- Documentar todas las clases públicas
- Incluir ejemplos de código
- Mantener información de compatibilidad
- Explicar patrones de integración

### Comentarios en Código
- PHPDoc completo en métodos públicos
- Explicar lógica compleja
- Documentar parámetros y tipos de retorno
- Incluir ejemplos cuando sea necesario

---

## 🚀 Flujo de Desarrollo con IA

### 1. Planificación
- Usar herramientas de gestión de tareas para proyectos complejos
- Desglosar trabajo en tareas de ~20 minutos
- Definir criterios de aceptación claros

### 2. Implementación
- Comenzar con tests (TDD cuando sea apropiado)
- Implementar funcionalidad mínima viable
- Refactorizar con feedback continuo

### 3. Validación
- Ejecutar suite completa de tests
- Verificar cobertura de código
- Revisar documentación actualizada
- Confirmar compatibilidad con versiones soportadas

### 4. Iteración
- Solicitar feedback del usuario
- Ajustar según necesidades
- Mantener historial de cambios claro

---

## ⚠️ Restricciones y Limitaciones

### Acciones Prohibidas sin Autorización
- Hacer commits o push de código
- Cambiar estado de tickets/issues
- Mergear branches
- Instalar nuevas dependencias
- Desplegar código a producción

### Principios de Seguridad
- No hardcodear credenciales
- Validar inputs de usuario
- Usar prepared statements
- Seguir principios de least privilege

---

## 🎨 Estilo de Código

### Laravel Conventions
- Usar Eloquent ORM para modelos
- Seguir naming conventions de Laravel
- Implementar Resource Controllers cuando aplique
- Usar Form Requests para validación

### PHP Standards
- PSR-12 para formato de código
- PSR-4 para autoloading
- Type hints obligatorios en PHP 8.1+
- Usar strict types cuando sea posible

---

*Este documento debe actualizarse conforme evolucione el proyecto y se identifiquen nuevos patrones o necesidades.*
