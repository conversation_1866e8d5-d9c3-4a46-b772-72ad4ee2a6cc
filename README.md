# LBCDev Ecommerce

Un paquete modular de comercio electrónico para Laravel que proporciona una solución flexible y desacoplada para manejar carritos, productos, variantes, impuestos, stock, y categorización por taxonomías.

---

## Índice

-  [Introducción](#introducción)
-  [Instalación](#instalación)
-  [Configuración](#configuración)
   -  [Publicar archivos](#publicar-archivos)
   -  [Configurar opciones](#configurar-opciones)
-  [Migraciones](#migraciones)
-  [Modelos y relaciones](#modelos-y-relaciones)
   -  [Producto](#producto)
   -  [Variantes](#variantes)
   -  [Carrito](#carrito)
   -  [Ítems del carrito](#ítems-del-carrito)
-  [Stock e inventario](#stock-e-inventario)
-  [Precios y SKUs](#precios-y-skus)
-  [Taxonomías](#taxonomías)
   -  [Uso del paquete `aliziodev/laravel-taxonomy`](#uso-del-paquete-aliziodevlaravel-taxonomy)
   -  [Trait `HasCategories`](#trait-hascategories)
-  [Eventos](#eventos)
-  [Comandos Artisan](#comandos-artisan)
-  [Extensión y personalización](#extensión-y-personalización)
-  [Pruebas](#pruebas)
-  [Contribuciones](#contribuciones)
-  [Licencia](#licencia)

---

## Introducción

`lbcdev/ecommerce` es un paquete para Laravel que proporciona las funcionalidades básicas necesarias para implementar una tienda en línea con un diseño desacoplado y extensible.

El paquete incluye:

-  Carrito persistente con soporte multi-guard y anónimo
-  Modelo de productos y variantes
-  Control de stock básico
-  Precios e impuestos por ítem
-  SKU obligatorio y heredado
-  Integración opcional con taxonomías (`aliziodev/laravel-taxonomy`)
-  Eventos personalizables
-  Comandos artisan útiles

---

## Instalación

### 1. Requisitos

-  Laravel 10+
-  PHP 8.1+
-  Base de datos MySQL, PostgreSQL u otra compatible con Laravel

### 2. Instalar el paquete principal

```bash
composer require lbcdev/ecommerce
```

Esto instalará también la dependencia [aliziodev/laravel-taxonomy](https://github.com/aliziodev/laravel-taxonomy)

### 3. Publicar archivos del paquete

```bash
php artisan vendor:publish --provider="LBCDev\Ecommerce\EcommerceServiceProvider"
```

ó

```bash
php artisan vendor:publish --tag="lbcdev-ecommerce-config"
php artisan vendor:publish --tag="lbcdev-ecommerce-views"
php artisan vendor:publish --tag="lbcdev-ecommerce-assets"
```

### 4. Ejecutar migraciones

#### Migraciones de ecommerce:

```bash
php artisan migrate
```

| 📌 Importante: debes migrar las tablas del ecommerce y de las taxonomías por separado.

#### Migraciones de taxonomías (si usarás taxonomías):

```bash
php artisan vendor:publish --provider="AlizioDev\LaravelTaxonomy\TaxonomyServiceProvider" --tag="migrations"
php artisan migrate
```

#### 🧩 Compatibilidad con taxonomías

Este paquete depende del paquete aliziodev/laravel-taxonomy, pero para garantizar compatibilidad con proyectos existentes, puedes:

Opción A: Ya tienes una tabla taxonomies
No es necesario crearla. Simplemente asegúrate de que contenga los siguientes campos mínimos:

-  id
-  name
-  slug
-  type
-  parent_id
-  timestamps

Opción B: Crear la tabla con la migración incluida
Publica la migración condicional si no tienes una tabla aún:

```bash
php artisan vendor:publish --tag="lbcdev-ecommerce-migrations"
```

Y luego:

```bash
php artisan migrate
```

## Test automatizados

El paquete incluye tests unitarios escritos con PHPUnit para garantizar la fiabilidad de las funcionalidades clave.

### Test unitarios

Para verificar que cada componente (unidad) del paquete funciona de manera aislada, hemos creado tests unitarios que puedes ejecutar de la siguiente manera:

```bash
vendor/bin/phpunit --testdox --testsuite=Unit
```

Para ejecutar los test con test coverage, usa el siguiente comando:

```bash
$env:XDEBUG_MODE = "coverage"
vendor/bin/phpunit --coverage-html=build/coverage --testdox --testsuite=Unit
```
