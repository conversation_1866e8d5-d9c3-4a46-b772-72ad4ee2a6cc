<?php

namespace App\Filament\Resources\Ecommerce\ProductResource\Pages;

use Filament\Actions;
use LBCDev\Ecommerce\Models\Tag;
use Illuminate\Support\Facades\Log;
use LBCDev\Ecommerce\Models\Category;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\Ecommerce\ProductResource;

class CreateProduct extends CreateRecord
{
    protected static string $resource = ProductResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Extraer categorías y tags
        $categories = $data['categories'] ?? [];
        $tags = $data['tags'] ?? [];

        // Remover del array de datos principales
        unset($data['categories'], $data['tags']);

        // Crear el modelo
        $model = static::getModel()::create($data);

        // Validar y sincronizar tags
        if (!empty($tags)) {
            $validTagIds = Tag::whereIn('id', $tags)->pluck('id')->toArray();

            if (!empty($validTagIds)) {
                $model->syncTags($validTagIds);
            }
        }

        // Validar y sincronizar categorías
        if (!empty($categories)) {
            $validCategoryIds = Category::whereIn('id', $categories)->pluck('id')->toArray();

            if (!empty($validCategoryIds)) {
                $model->syncCategories($validCategoryIds);
            }
        }



        return $model;
    }
}
