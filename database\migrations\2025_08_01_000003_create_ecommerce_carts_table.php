<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('lbcdev_ecommerce_carts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('session_id')->nullable();
            $table->string('identifier')->unique();
            $table->decimal('total', 10, 2)->default(0);
            $table->decimal('total_with_tax', 10, 2)->default(0);
            $table->timestamp('expires_at');
            $table->timestamps();

            // Índices
            $table->index('user_id');
            $table->index('session_id');
            $table->index('identifier');
            $table->index('expires_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('lbcdev_ecommerce_carts');
    }
};
