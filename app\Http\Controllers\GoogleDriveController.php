<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Services\GoogleDriveService;
use LBCDev\OAuthManager\Models\OAuthService;

class GoogleDriveController extends Controller
{
    /**
     * List files in a Google Drive folder
     */
    public function listFiles(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'oauth_service_id' => 'required|exists:oauth_services,id',
                'folder_id' => 'nullable|string',
                'page_size' => 'nullable|integer|min:1|max:100'
            ]);

            $oauthService = OAuthService::findOrFail($request->oauth_service_id);
            
            if (!$oauthService->is_active || !$oauthService->access_token) {
                return response()->json([
                    'error' => 'OAuth service is not active or missing access token'
                ], 400);
            }

            $driveService = new GoogleDriveService($oauthService);
            $folderId = $request->get('folder_id', 'root');
            $pageSize = $request->get('page_size', 50);

            $result = $driveService->listFiles($folderId, $pageSize);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to list files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file information
     */
    public function getFile(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'oauth_service_id' => 'required|exists:oauth_services,id',
                'file_id' => 'required|string'
            ]);

            $oauthService = OAuthService::findOrFail($request->oauth_service_id);
            
            if (!$oauthService->is_active || !$oauthService->access_token) {
                return response()->json([
                    'error' => 'OAuth service is not active or missing access token'
                ], 400);
            }

            $driveService = new GoogleDriveService($oauthService);
            $fileData = $driveService->getFile($request->file_id);

            return response()->json([
                'success' => true,
                'data' => $fileData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to get file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get folder breadcrumb
     */
    public function getBreadcrumb(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'oauth_service_id' => 'required|exists:oauth_services,id',
                'folder_id' => 'required|string'
            ]);

            $oauthService = OAuthService::findOrFail($request->oauth_service_id);
            
            if (!$oauthService->is_active || !$oauthService->access_token) {
                return response()->json([
                    'error' => 'OAuth service is not active or missing access token'
                ], 400);
            }

            $driveService = new GoogleDriveService($oauthService);
            $breadcrumb = $driveService->getBreadcrumb($request->folder_id);

            return response()->json([
                'success' => true,
                'data' => $breadcrumb
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to get breadcrumb: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search files
     */
    public function searchFiles(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'oauth_service_id' => 'required|exists:oauth_services,id',
                'query' => 'required|string|min:1',
                'folder_id' => 'nullable|string'
            ]);

            $oauthService = OAuthService::findOrFail($request->oauth_service_id);
            
            if (!$oauthService->is_active || !$oauthService->access_token) {
                return response()->json([
                    'error' => 'OAuth service is not active or missing access token'
                ], 400);
            }

            $driveService = new GoogleDriveService($oauthService);
            $folderId = $request->get('folder_id', 'root');
            $files = $driveService->searchFiles($request->query, $folderId);

            return response()->json([
                'success' => true,
                'data' => $files
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to search files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test connection to Google Drive
     */
    public function testConnection(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'oauth_service_id' => 'required|exists:oauth_services,id'
            ]);

            $oauthService = OAuthService::findOrFail($request->oauth_service_id);
            
            if (!$oauthService->is_active || !$oauthService->access_token) {
                return response()->json([
                    'error' => 'OAuth service is not active or missing access token'
                ], 400);
            }

            $driveService = new GoogleDriveService($oauthService);
            $isConnected = $driveService->testConnection();

            return response()->json([
                'success' => true,
                'connected' => $isConnected
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to test connection: ' . $e->getMessage()
            ], 500);
        }
    }
}
