<?php

namespace LBCDev\Ecommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ProductLinkable extends Model
{
    protected $table = 'lbcdev_ecommerce_productables';

    protected $fillable = [
        'product_id',
        'productable_type',
        'productable_id',
        'purpose',
        'access_policy',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array',
    ];

    /**
     * Relación con el producto
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Relación polimórfica con el modelo linkable
     */
    public function productable(): MorphTo
    {
        return $this->morphTo();
    }
}
