# OAuth File Explorer Components

Este documento describe los componentes de Filament creados para explorar archivos de diferentes servicios OAuth (Google Drive, OneDrive, Dropbox) usando la dependencia `lbcdev/oauth-file-explorer`.

## Componentes Disponibles

### 1. OAuthFileExplorer (Componente Genérico)

El componente genérico que puede manejar cualquier tipo de servicio OAuth automáticamente.

```php
use App\Filament\Forms\Components\OAuthFileExplorer;

OAuthFileExplorer::make('url')
    ->label('File URL')
    ->oauthServiceField('oauth_service_id')
    ->required()
    ->helperText('Browse and select a file from your cloud storage service')
```

### 2. GoogleDriveExplorer

Componente específico para Google Drive (mantiene compatibilidad con implementaciones existentes).

```php
use App\Filament\Forms\Components\GoogleDriveExplorer;

GoogleDriveExplorer::make('url')
    ->label('Google Drive File')
    ->oauthServiceField('oauth_service_id')
    ->required()
    ->acceptImages() // Solo acepta imágenes
```

### 3. OneDriveExplorer

Componente específico para OneDrive.

```php
use App\Filament\Forms\Components\OneDriveExplorer;

OneDriveExplorer::make('url')
    ->label('OneDrive File')
    ->oauthServiceField('oauth_service_id')
    ->required()
    ->acceptDocuments() // Solo acepta documentos
```

### 4. DropboxExplorer

Componente específico para Dropbox.

```php
use App\Filament\Forms\Components\DropboxExplorer;

DropboxExplorer::make('url')
    ->label('Dropbox File')
    ->oauthServiceField('oauth_service_id')
    ->required()
    ->acceptVideos() // Solo acepta videos
```

## Métodos Disponibles

Todos los componentes comparten los siguientes métodos:

### Configuración del Servicio OAuth

```php
->oauthServiceField('oauth_service_id')
```

Especifica el campo que contiene el ID del servicio OAuth.

### Filtros de Tipo de Archivo

```php
// Tipos predefinidos
->acceptImages()      // jpeg, png, gif, webp, svg
->acceptDocuments()   // pdf, doc, docx, xls, xlsx, ppt, pptx, txt, csv
->acceptVideos()      // mp4, avi, mov, wmv, flv, webm, mkv
->acceptAudio()       // mp3, wav, ogg, aac, flac, wma

// Tipos personalizados
->acceptedMimeTypes(['application/pdf', 'image/jpeg'])
```

### Configuración de Visualización

```php
->showSelectedFileInfo(true)    // Mostrar información del archivo seleccionado
->allowFolderSelection(false)   // Permitir selección de carpetas
```

## Ejemplo de Uso en FileResource

```php
<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\File;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use App\Filament\Forms\Components\OAuthFileExplorer;
use Filament\Forms\Components\Section;

class FileResource extends Resource
{
    protected static ?string $model = File::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nombre')->required(),

                Section::make('Configuración de almacenamiento')
                    ->schema([
                        Forms\Components\Select::make('oauth_service_id')
                            ->relationship('oauthService', 'name', 
                                fn($query) => $query->where('is_active', true)
                                                   ->where('access_token', '!=', null)
                            )
                            ->live()
                            ->required()
                            ->label('OAuth Service')
                            ->helperText('Select the cloud storage service to browse files from')
                            ->getOptionLabelFromRecordUsing(
                                fn($record) => $record->name . ' (' . 
                                              ucfirst(str_replace('_', ' ', $record->service_type)) . 
                                              ')'
                            ),

                        OAuthFileExplorer::make('url')
                            ->label('File URL')
                            ->oauthServiceField('oauth_service_id')
                            ->required()
                            ->helperText('Browse and select a file from your cloud storage service'),
                    ]),
                
                // Otros campos...
            ]);
    }
}
```

## Características

- **Detección automática del tipo de servicio**: El componente genérico detecta automáticamente si es Google Drive, OneDrive, Dropbox, etc.
- **Filtrado de tipos de archivo**: Permite restringir qué tipos de archivos pueden ser seleccionados
- **Navegación de carpetas**: Soporte completo para navegar por la estructura de carpetas
- **Búsqueda de archivos**: Funcionalidad de búsqueda integrada
- **Breadcrumbs**: Navegación visual de la ruta actual
- **Interfaz responsive**: Diseño adaptable para diferentes tamaños de pantalla

## Dependencias

- `lbcdev/oauth-file-explorer`: Proporciona la funcionalidad base de exploración
- `lbcdev/oauth-manager`: Maneja la autenticación OAuth con los diferentes servicios

## Archivos Creados

- `app/Filament/Forms/Components/OAuthFileExplorer.php`
- `app/Filament/Forms/Components/OneDriveExplorer.php`
- `app/Filament/Forms/Components/DropboxExplorer.php`
- `resources/views/filament/forms/components/oauth-file-explorer.blade.php`
- `resources/views/filament/forms/components/onedrive-explorer.blade.php`
- `resources/views/filament/forms/components/dropbox-explorer.blade.php`

## Recomendación

Se recomienda usar el componente genérico `OAuthFileExplorer` para nuevas implementaciones, ya que maneja automáticamente todos los tipos de servicios OAuth y es más flexible.
