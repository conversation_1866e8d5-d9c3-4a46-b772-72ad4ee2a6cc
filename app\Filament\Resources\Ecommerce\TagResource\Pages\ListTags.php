<?php

namespace App\Filament\Resources\Ecommerce\TagResource\Pages;

use App\Filament\Resources\Ecommerce\TagResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTags extends ListRecords
{
    protected static string $resource = TagResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
