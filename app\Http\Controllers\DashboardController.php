<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function index()
    {
        /** @var User */
        $user = Auth::user();

        if (!$user || (!$user->hasRole('admin') && !$user->hasRole('cliente'))) {
            return redirect()->route('home');
        }

        $redirectUrl = $user->hasRole('admin') ? '/admin' : '/app';

        return view('dashboard-redirect', compact('redirectUrl'));
    }
}
