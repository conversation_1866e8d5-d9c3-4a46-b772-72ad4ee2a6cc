# Estructura para Paquete Composer

## Organización propuesta

Cuando se extraiga a un paquete Composer independiente, la estructura sería:

```
lbcdev/filament-file-explorer/
├── src/
│   ├── Contracts/
│   │   └── FileExplorerInterface.php
│   ├── Services/
│   │   ├── AbstractFileExplorer.php
│   │   ├── FileExplorerFactory.php
│   │   └── Explorers/
│   │       ├── GoogleDriveExplorer.php
│   │       ├── DropboxExplorer.php
│   │       ├── OneDriveExplorer.php
│   │       └── YouTubeExplorer.php
│   ├── Filament/
│   │   └── Forms/
│   │       └── Components/
│   │           └── FileExplorer.php
│   ├── Livewire/
│   │   └── FileExplorer.php
│   └── FileExplorerServiceProvider.php
├── resources/
│   └── views/
│       ├── filament/
│       │   └── forms/
│       │       └── components/
│       │           └── file-explorer.blade.php
│       └── livewire/
│           └── file-explorer.blade.php
├── config/
│   └── filament-file-explorer.php
├── docs/
│   ├── installation.md
│   ├── usage.md
│   └── extending.md
├── tests/
│   ├── Unit/
│   └── Feature/
├── composer.json
├── README.md
└── CHANGELOG.md
```

## Archivos a mover

### Desde el proyecto actual:

1. **Contratos**:
   - `app/Contracts/FileExplorerInterface.php` → `src/Contracts/FileExplorerInterface.php`

2. **Servicios**:
   - `app/Services/FileExplorers/AbstractFileExplorer.php` → `src/Services/AbstractFileExplorer.php`
   - `app/Services/FileExplorers/FileExplorerFactory.php` → `src/Services/FileExplorerFactory.php`
   - `app/Services/FileExplorers/DropboxExplorer.php` → `src/Services/Explorers/DropboxExplorer.php`
   - `app/Services/GoogleDriveService.php` → `src/Services/Explorers/GoogleDriveExplorer.php`

3. **Componentes Filament**:
   - `app/Filament/Forms/Components/FileExplorer.php` → `src/Filament/Forms/Components/FileExplorer.php`

4. **Componentes Livewire**:
   - `app/Livewire/FileExplorer.php` → `src/Livewire/FileExplorer.php`

5. **Vistas**:
   - `resources/views/filament/forms/components/file-explorer.blade.php` → `resources/views/filament/forms/components/file-explorer.blade.php`
   - `resources/views/livewire/file-explorer.blade.php` → `resources/views/livewire/file-explorer.blade.php`

## Composer.json propuesto

```json
{
    "name": "lbcdev/filament-file-explorer",
    "description": "Extensible file explorer component for Filament with support for multiple cloud storage services",
    "type": "library",
    "license": "MIT",
    "authors": [
        {
            "name": "LBCDev",
            "email": "<EMAIL>"
        }
    ],
    "require": {
        "php": "^8.1",
        "filament/filament": "^3.0",
        "livewire/livewire": "^3.0",
        "lbcdev/oauth-manager": "^1.0"
    },
    "require-dev": {
        "orchestra/testbench": "^8.0",
        "phpunit/phpunit": "^10.0"
    },
    "autoload": {
        "psr-4": {
            "LBCDev\\FilamentFileExplorer\\": "src/"
        }
    },
    "autoload-dev": {
        "psr-4": {
            "LBCDev\\FilamentFileExplorer\\Tests\\": "tests/"
        }
    },
    "extra": {
        "laravel": {
            "providers": [
                "LBCDev\\FilamentFileExplorer\\FileExplorerServiceProvider"
            ]
        }
    },
    "minimum-stability": "dev",
    "prefer-stable": true
}
```

## Service Provider

```php
<?php

namespace LBCDev\FilamentFileExplorer;

use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;

class FileExplorerServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->mergeConfigFrom(
            __DIR__.'/../config/filament-file-explorer.php',
            'filament-file-explorer'
        );
    }

    public function boot()
    {
        // Publish config
        $this->publishes([
            __DIR__.'/../config/filament-file-explorer.php' => config_path('filament-file-explorer.php'),
        ], 'filament-file-explorer-config');

        // Publish views
        $this->publishes([
            __DIR__.'/../resources/views' => resource_path('views/vendor/filament-file-explorer'),
        ], 'filament-file-explorer-views');

        // Load views
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'filament-file-explorer');

        // Register Livewire component
        Livewire::component('file-explorer', \LBCDev\FilamentFileExplorer\Livewire\FileExplorer::class);
    }
}
```

## Configuración

```php
<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Explorer Settings
    |--------------------------------------------------------------------------
    */
    'default_page_size' => 50,
    
    /*
    |--------------------------------------------------------------------------
    | Supported Services
    |--------------------------------------------------------------------------
    */
    'supported_services' => [
        'google_drive',
        'dropbox',
        'onedrive',
        'youtube',
        'mega',
    ],

    /*
    |--------------------------------------------------------------------------
    | Explorer Classes
    |--------------------------------------------------------------------------
    */
    'explorers' => [
        'google_drive' => \LBCDev\FilamentFileExplorer\Services\Explorers\GoogleDriveExplorer::class,
        'dropbox' => \LBCDev\FilamentFileExplorer\Services\Explorers\DropboxExplorer::class,
        // Add more explorers here
    ],

    /*
    |--------------------------------------------------------------------------
    | File Type Filters
    |--------------------------------------------------------------------------
    */
    'mime_types' => [
        'documents' => [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            // ... more document types
        ],
        'images' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            // ... more image types
        ],
        'videos' => [
            'video/mp4',
            'video/avi',
            'video/mov',
            // ... more video types
        ],
    ],
];
```

## Instalación

```bash
composer require lbcdev/filament-file-explorer
```

## Publicación de assets

```bash
# Publicar configuración
php artisan vendor:publish --tag=filament-file-explorer-config

# Publicar vistas (opcional, para personalización)
php artisan vendor:publish --tag=filament-file-explorer-views
```

## Dependencias

- **lbcdev/oauth-manager**: Para gestión de tokens OAuth
- **filament/filament**: Framework de administración
- **livewire/livewire**: Para componentes reactivos
- **spatie/dropbox-api**: Para integración con Dropbox (opcional)
- **google/apiclient**: Para integración con Google Drive (opcional)

## Beneficios del paquete

1. **Reutilizable**: Se puede usar en múltiples proyectos Laravel/Filament
2. **Extensible**: Fácil agregar nuevos servicios de almacenamiento
3. **Mantenible**: Código centralizado y versionado
4. **Testeable**: Estructura preparada para tests unitarios y de integración
5. **Documentado**: Documentación completa para usuarios y desarrolladores

## Migración desde el sistema actual

Para migrar el código actual al paquete:

1. Crear la estructura del paquete
2. Mover archivos según el mapeo definido
3. Actualizar namespaces
4. Crear el service provider
5. Configurar composer.json
6. Crear tests
7. Documentar instalación y uso
