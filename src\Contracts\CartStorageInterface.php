<?php

namespace LBCDev\Ecommerce\Contracts;

use LBCDev\Ecommerce\Models\Cart;

interface CartStorageInterface
{
    /**
     * Obtener un carrito por identificador.
     */
    public function getCart(string $identifier): Cart;

    /**
     * Guardar un carrito.
     */
    public function saveCart(string $identifier, Cart $cart): void;

    /**
     * Limpiar un carrito.
     */
    public function clearCart(string $identifier): void;

    /**
     * Verificar si existe un carrito.
     */
    public function hasCart(string $identifier): bool;

    /**
     * Fusionar dos carritos.
     */
    public function mergeCarts(string $fromIdentifier, string $toIdentifier): Cart;
}
