<?php

namespace LBCDev\Ecommerce\Models;

use LBCDev\Ecommerce\Models\Order;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use LBCDev\Ecommerce\Database\Factories\OrderItemFactory;

class OrderItem extends Model
{
    use HasFactory;

    protected $table = 'lbcdev_ecommerce_order_items';

    protected $fillable = [
        'order_id',
        'sellable_type',
        'sellable_id',
        'quantity',
        'unit_price',
        'tax_rate',
        'subtotal',
        'tax_amount',
        'total',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'float',
        'tax_rate' => 'float',
        'subtotal' => 'float',
        'tax_amount' => 'float',
        'total' => 'float',
    ];

    /**
     * Relación con la orden.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Relación polimórfica con el item vendible.
     */
    public function sellable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Verificar si el item original aún existe.
     */
    public function hasOriginalItem(): bool
    {
        return $this->sellable !== null;
    }

    /**
     * Obtener el precio unitario con impuestos.
     */
    public function getUnitPriceWithTax(): float
    {
        return $this->unit_price * (1 + $this->tax_rate / 100);
    }

    protected static function newFactory()
    {
        return OrderItemFactory::new();
    }
}
